import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:flutter/foundation.dart';

/// Service pour gérer la migration des données entre versions
class DataMigrationService {
  static final DataMigrationService _instance =
      DataMigrationService._internal();
  factory DataMigrationService() => _instance;
  DataMigrationService._internal();

  static DataMigrationService get instance => _instance;

  static const String _migrationVersionKey = 'data_migration_version';
  static const String _dataBackupKey = 'data_backup_';

  /// Migrer les données vers la version actuelle avec timeout
  Future<void> migrateToCurrentVersion(String currentVersion) async {
    try {
      // Ajouter un timeout pour éviter le blocage
      await _performMigrationWithTimeout(currentVersion);
    } catch (e) {
      debugPrint('❌ Erreur lors de la migration (timeout ou autre): $e');
      // En cas d'erreur, continuer quand même avec une initialisation basique
      await _ensureBasicDataStructures();
    }
  }

  /// Effectuer la migration avec timeout
  Future<void> _performMigrationWithTimeout(String currentVersion) async {
    final prefs = await SharedPreferences.getInstance();
    final lastMigrationVersion = prefs.getString(_migrationVersionKey);

    debugPrint('🔄 Migration des données vers $currentVersion');
    debugPrint('📋 Dernière migration: ${lastMigrationVersion ?? "aucune"}');

    // Effectuer les migrations nécessaires
    if (lastMigrationVersion == null) {
      // Première installation - initialiser les données
      await _initializeForFirstInstall();
    } else if (lastMigrationVersion != currentVersion) {
      // Migration depuis une version précédente
      await _migrateFromVersion(lastMigrationVersion, currentVersion);
    }

    // Marquer la migration comme terminée
    await prefs.setString(_migrationVersionKey, currentVersion);
    debugPrint('✅ Migration terminée vers $currentVersion');
  }

  /// S'assurer que les structures de données de base existent (fallback)
  Future<void> _ensureBasicDataStructures() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Initialiser seulement les structures essentielles
      final basicData = {
        'invoices': '[]',
        'products': '[]',
        'categories': '[]',
        'tasks': '[]',
        'colis': '[]',
      };

      for (final entry in basicData.entries) {
        if (!prefs.containsKey(entry.key)) {
          await prefs.setString(entry.key, entry.value);
        }
      }

      await prefs.setBool('local_data_initialized', true);
      debugPrint('📦 Structures de données de base créées');
    } catch (e) {
      debugPrint('❌ Erreur lors de la création des structures de base: $e');
    }
  }

  /// Initialiser les données pour une première installation
  Future<void> _initializeForFirstInstall() async {
    final prefs = await SharedPreferences.getInstance();

    debugPrint('📦 Initialisation pour première installation');

    // Initialiser toutes les structures de données nécessaires
    final defaultData = {
      'invoices': '[]',
      'products': '[]',
      'categories': '[]',
      'tasks': '[]',
      'colis': '[]',
      'users': '[]',
      'clients': '[]',
      'suppliers': '[]',
    };

    for (final entry in defaultData.entries) {
      if (!prefs.containsKey(entry.key)) {
        await prefs.setString(entry.key, entry.value);
      }
    }

    // Paramètres par défaut
    final defaultSettings = {
      'monthly_target': 1000000.0,
      'yearly_target': 12000000.0,
      'currency': 'FCFA',
      'company_name': 'General HCP CRM',
      'first_launch_completed': true,
      'local_data_initialized': true,
      'offline_mode_enabled': true,
    };

    for (final entry in defaultSettings.entries) {
      if (!prefs.containsKey(entry.key)) {
        if (entry.value is double) {
          await prefs.setDouble(entry.key, entry.value as double);
        } else if (entry.value is bool) {
          await prefs.setBool(entry.key, entry.value as bool);
        } else {
          await prefs.setString(entry.key, entry.value.toString());
        }
      }
    }

    debugPrint('✅ Données initialisées pour première installation');
  }

  /// Migrer depuis une version spécifique
  Future<void> _migrateFromVersion(String fromVersion, String toVersion) async {
    debugPrint('🔄 Migration de $fromVersion vers $toVersion');

    // Appliquer les migrations nécessaires selon les versions
    final migrations = _getMigrationsNeeded(fromVersion, toVersion);

    for (final migration in migrations) {
      debugPrint('📝 Application de la migration: ${migration.description}');
      await migration.apply();
    }
  }

  /// Obtenir la liste des migrations nécessaires
  List<DataMigration> _getMigrationsNeeded(
    String fromVersion,
    String toVersion,
  ) {
    final migrations = <DataMigration>[];

    // Migration vers 2.0.0
    if (_shouldApplyMigration(fromVersion, '2.0.0', toVersion)) {
      migrations.add(MigrationTo200());
    }

    // Migration vers 2.0.1
    if (_shouldApplyMigration(fromVersion, '2.0.1', toVersion)) {
      migrations.add(MigrationTo201());
    }

    // Migration vers 2.1.0 (future)
    if (_shouldApplyMigration(fromVersion, '2.1.0', toVersion)) {
      migrations.add(MigrationTo210());
    }

    return migrations;
  }

  /// Vérifier si une migration doit être appliquée
  bool _shouldApplyMigration(
    String fromVersion,
    String migrationVersion,
    String toVersion,
  ) {
    return _compareVersions(fromVersion, migrationVersion) < 0 &&
        _compareVersions(migrationVersion, toVersion) <= 0;
  }

  /// Comparer deux versions (retourne -1, 0, ou 1)
  int _compareVersions(String version1, String version2) {
    final v1Parts = version1.split('.').map(int.parse).toList();
    final v2Parts = version2.split('.').map(int.parse).toList();

    for (int i = 0; i < 3; i++) {
      final v1Part = i < v1Parts.length ? v1Parts[i] : 0;
      final v2Part = i < v2Parts.length ? v2Parts[i] : 0;

      if (v1Part < v2Part) return -1;
      if (v1Part > v2Part) return 1;
    }

    return 0;
  }

  /// Sauvegarder les données actuelles
  Future<void> _backupCurrentData(String version) async {
    final prefs = await SharedPreferences.getInstance();
    final backupKey = '$_dataBackupKey$version';

    final dataToBackup = <String, dynamic>{};

    // Sauvegarder toutes les données importantes
    final keysToBackup = [
      'invoices',
      'products',
      'categories',
      'tasks',
      'colis',
      'users',
      'clients',
      'suppliers',
      'monthly_target',
      'yearly_target',
      'dashboard_card_order',
      'offline_mode_enabled',
    ];

    for (final key in keysToBackup) {
      if (prefs.containsKey(key)) {
        final value = prefs.get(key);
        dataToBackup[key] = value;
      }
    }

    await prefs.setString(backupKey, jsonEncode(dataToBackup));
    debugPrint('💾 Sauvegarde créée: $backupKey');
  }

  /// Restaurer une sauvegarde
  Future<void> _restoreBackup(String version) async {
    final prefs = await SharedPreferences.getInstance();
    final backupKey = '$_dataBackupKey$version';

    if (prefs.containsKey(backupKey)) {
      final backupData = jsonDecode(prefs.getString(backupKey)!);

      for (final entry in backupData.entries) {
        if (entry.value is String) {
          await prefs.setString(entry.key, entry.value);
        } else if (entry.value is double) {
          await prefs.setDouble(entry.key, entry.value as double);
        } else if (entry.value is bool) {
          await prefs.setBool(entry.key, entry.value as bool);
        } else if (entry.value is int) {
          await prefs.setInt(entry.key, entry.value);
        }
      }

      debugPrint('🔄 Sauvegarde restaurée: $backupKey');
    }
  }

  /// Vérifier l'intégrité des données
  Future<bool> verifyDataIntegrity() async {
    final prefs = await SharedPreferences.getInstance();

    try {
      // Vérifier que les structures de données essentielles existent
      final requiredKeys = ['invoices', 'products', 'categories', 'tasks'];

      for (final key in requiredKeys) {
        if (!prefs.containsKey(key)) {
          debugPrint('❌ Clé manquante: $key');
          return false;
        }

        // Vérifier que c'est du JSON valide
        final value = prefs.getString(key);
        if (value != null) {
          jsonDecode(value);
        }
      }

      debugPrint('✅ Intégrité des données vérifiée');
      return true;
    } catch (e) {
      debugPrint('❌ Erreur lors de la vérification: $e');
      return false;
    }
  }

  /// Nettoyer les anciennes sauvegardes
  Future<void> cleanupOldBackups() async {
    final prefs = await SharedPreferences.getInstance();
    final allKeys = prefs.getKeys();

    final backupKeys =
        allKeys.where((key) => key.startsWith(_dataBackupKey)).toList();

    // Garder seulement les 3 dernières sauvegardes
    if (backupKeys.length > 3) {
      backupKeys.sort();
      final keysToRemove = backupKeys.take(backupKeys.length - 3);

      for (final key in keysToRemove) {
        await prefs.remove(key);
        debugPrint('🗑️ Sauvegarde supprimée: $key');
      }
    }
  }
}

/// Interface pour les migrations de données
abstract class DataMigration {
  String get description;
  Future<void> apply();
}

/// Migration vers la version 2.0.0
class MigrationTo200 extends DataMigration {
  @override
  String get description =>
      'Migration vers 2.0.0 - Suppression doublon objectif mensuel';

  @override
  Future<void> apply() async {
    final prefs = await SharedPreferences.getInstance();

    // Supprimer les anciennes clés de cache
    await prefs.remove('monthly_objective_cache');
    await prefs.remove('duplicate_monthly_card');

    debugPrint('✅ Migration 2.0.0 appliquée');
  }
}

/// Migration vers la version 2.0.1
class MigrationTo201 extends DataMigration {
  @override
  String get description => 'Migration vers 2.0.1 - Système de versioning';

  @override
  Future<void> apply() async {
    final prefs = await SharedPreferences.getInstance();

    // Initialiser les paramètres de versioning
    if (!prefs.containsKey('offline_mode_enabled')) {
      await prefs.setBool('offline_mode_enabled', true);
    }

    debugPrint('✅ Migration 2.0.1 appliquée');
  }
}

/// Migration vers la version 2.1.0 (future)
class MigrationTo210 extends DataMigration {
  @override
  String get description => 'Migration vers 2.1.0 - Nouvelles fonctionnalités';

  @override
  Future<void> apply() async {
    // Migrations futures
    debugPrint('✅ Migration 2.1.0 appliquée');
  }
}
