import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/invoice.dart';
import '../models/product.dart';
import '../models/category.dart' as model;
import '../models/task.dart';
import '../models/colis.dart';

/// Service pour charger toutes les données locales de manière robuste
class LocalDataLoader {
  static final LocalDataLoader _instance = LocalDataLoader._internal();
  factory LocalDataLoader() => _instance;
  LocalDataLoader._internal();

  static LocalDataLoader get instance => _instance;

  /// Charger toutes les données locales
  Future<LocalDataSnapshot> loadAllData() async {
    debugPrint('📦 Chargement de toutes les données locales...');
    
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final snapshot = LocalDataSnapshot(
        invoices: await _loadInvoices(prefs),
        products: await _loadProducts(prefs),
        categories: await _loadCategories(prefs),
        tasks: await _loadTasks(prefs),
        colis: await _loadColis(prefs),
        settings: await _loadSettings(prefs),
        stats: await _loadStats(prefs),
      );

      debugPrint('✅ Données locales chargées: ${snapshot.summary}');
      return snapshot;
      
    } catch (e) {
      debugPrint('❌ Erreur lors du chargement des données: $e');
      // Retourner un snapshot vide en cas d'erreur
      return LocalDataSnapshot.empty();
    }
  }

  /// Charger les factures
  Future<List<Invoice>> _loadInvoices(SharedPreferences prefs) async {
    try {
      final invoicesJson = prefs.getString('invoices') ?? '[]';
      final invoicesList = jsonDecode(invoicesJson) as List;
      return invoicesList.map((json) => Invoice.fromJson(json)).toList();
    } catch (e) {
      debugPrint('⚠️ Erreur lors du chargement des factures: $e');
      return [];
    }
  }

  /// Charger les produits
  Future<List<Product>> _loadProducts(SharedPreferences prefs) async {
    try {
      final productsJson = prefs.getString('products') ?? '[]';
      final productsList = jsonDecode(productsJson) as List;
      return productsList.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      debugPrint('⚠️ Erreur lors du chargement des produits: $e');
      return [];
    }
  }

  /// Charger les catégories
  Future<List<model.Category>> _loadCategories(SharedPreferences prefs) async {
    try {
      final categoriesJson = prefs.getString('categories') ?? '[]';
      final categoriesList = jsonDecode(categoriesJson) as List;
      return categoriesList.map((json) => model.Category.fromJson(json)).toList();
    } catch (e) {
      debugPrint('⚠️ Erreur lors du chargement des catégories: $e');
      return [];
    }
  }

  /// Charger les tâches
  Future<List<Task>> _loadTasks(SharedPreferences prefs) async {
    try {
      final tasksJson = prefs.getString('tasks') ?? '[]';
      final tasksList = jsonDecode(tasksJson) as List;
      return tasksList.map((json) => Task.fromJson(json)).toList();
    } catch (e) {
      debugPrint('⚠️ Erreur lors du chargement des tâches: $e');
      return [];
    }
  }

  /// Charger les colis
  Future<List<Colis>> _loadColis(SharedPreferences prefs) async {
    try {
      final colisJson = prefs.getString('colis') ?? '[]';
      final colisList = jsonDecode(colisJson) as List;
      return colisList.map((json) => Colis.fromJson(json)).toList();
    } catch (e) {
      debugPrint('⚠️ Erreur lors du chargement des colis: $e');
      return [];
    }
  }

  /// Charger les paramètres
  Future<Map<String, dynamic>> _loadSettings(SharedPreferences prefs) async {
    try {
      return {
        'monthlyTarget': prefs.getDouble('monthly_target') ?? 1000000.0,
        'yearlyTarget': prefs.getDouble('yearly_target') ?? 12000000.0,
        'currency': prefs.getString('currency') ?? 'FCFA',
        'companyName': prefs.getString('company_name') ?? 'General HCP CRM',
        'offlineModeEnabled': prefs.getBool('offline_mode_enabled') ?? true,
        'firstLaunchCompleted': prefs.getBool('first_launch_completed') ?? false,
        'localDataInitialized': prefs.getBool('local_data_initialized') ?? false,
      };
    } catch (e) {
      debugPrint('⚠️ Erreur lors du chargement des paramètres: $e');
      return {};
    }
  }

  /// Charger les statistiques
  Future<Map<String, dynamic>> _loadStats(SharedPreferences prefs) async {
    try {
      return {
        'totalInvoices': prefs.getInt('total_invoices') ?? 0,
        'totalRevenue': prefs.getDouble('total_revenue') ?? 0.0,
        'totalProducts': prefs.getInt('total_products') ?? 0,
        'totalTasks': prefs.getInt('total_tasks') ?? 0,
        'totalColis': prefs.getInt('total_colis') ?? 0,
        'lastUpdateTime': prefs.getString('last_update_time'),
      };
    } catch (e) {
      debugPrint('⚠️ Erreur lors du chargement des statistiques: $e');
      return {};
    }
  }

  /// Sauvegarder toutes les données
  Future<bool> saveAllData(LocalDataSnapshot snapshot) async {
    try {
      debugPrint('💾 Sauvegarde de toutes les données locales...');
      
      final prefs = await SharedPreferences.getInstance();
      
      // Sauvegarder les données
      await prefs.setString('invoices', jsonEncode(snapshot.invoices.map((e) => e.toJson()).toList()));
      await prefs.setString('products', jsonEncode(snapshot.products.map((e) => e.toJson()).toList()));
      await prefs.setString('categories', jsonEncode(snapshot.categories.map((e) => e.toJson()).toList()));
      await prefs.setString('tasks', jsonEncode(snapshot.tasks.map((e) => e.toJson()).toList()));
      await prefs.setString('colis', jsonEncode(snapshot.colis.map((e) => e.toJson()).toList()));
      
      // Sauvegarder les paramètres
      for (final entry in snapshot.settings.entries) {
        if (entry.value is double) {
          await prefs.setDouble(entry.key, entry.value as double);
        } else if (entry.value is bool) {
          await prefs.setBool(entry.key, entry.value as bool);
        } else if (entry.value is int) {
          await prefs.setInt(entry.key, entry.value);
        } else {
          await prefs.setString(entry.key, entry.value.toString());
        }
      }
      
      // Mettre à jour l'horodatage
      await prefs.setString('last_update_time', DateTime.now().toIso8601String());
      
      debugPrint('✅ Données sauvegardées avec succès');
      return true;
      
    } catch (e) {
      debugPrint('❌ Erreur lors de la sauvegarde: $e');
      return false;
    }
  }

  /// Vérifier si les données locales existent
  Future<bool> hasLocalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('local_data_initialized') ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Obtenir un résumé des données
  Future<String> getDataSummary() async {
    final snapshot = await loadAllData();
    return snapshot.summary;
  }

  /// Réinitialiser toutes les données (pour debug)
  Future<void> resetAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Supprimer toutes les données
      final keysToRemove = [
        'invoices', 'products', 'categories', 'tasks', 'colis',
        'users', 'clients', 'suppliers', 'total_invoices', 'total_revenue',
        'total_products', 'total_tasks', 'total_colis', 'last_update_time',
      ];
      
      for (final key in keysToRemove) {
        await prefs.remove(key);
      }
      
      // Marquer comme non initialisé
      await prefs.setBool('local_data_initialized', false);
      
      debugPrint('🗑️ Toutes les données locales ont été réinitialisées');
    } catch (e) {
      debugPrint('❌ Erreur lors de la réinitialisation: $e');
    }
  }
}

/// Snapshot des données locales
class LocalDataSnapshot {
  final List<Invoice> invoices;
  final List<Product> products;
  final List<model.Category> categories;
  final List<Task> tasks;
  final List<Colis> colis;
  final Map<String, dynamic> settings;
  final Map<String, dynamic> stats;

  LocalDataSnapshot({
    required this.invoices,
    required this.products,
    required this.categories,
    required this.tasks,
    required this.colis,
    required this.settings,
    required this.stats,
  });

  /// Créer un snapshot vide
  factory LocalDataSnapshot.empty() {
    return LocalDataSnapshot(
      invoices: [],
      products: [],
      categories: [],
      tasks: [],
      colis: [],
      settings: {},
      stats: {},
    );
  }

  /// Obtenir un résumé des données
  String get summary {
    return '${invoices.length} factures, ${products.length} produits, '
           '${categories.length} catégories, ${tasks.length} tâches, '
           '${colis.length} colis';
  }

  /// Vérifier si le snapshot est vide
  bool get isEmpty {
    return invoices.isEmpty && products.isEmpty && categories.isEmpty && 
           tasks.isEmpty && colis.isEmpty;
  }

  /// Obtenir le nombre total d'éléments
  int get totalItems {
    return invoices.length + products.length + categories.length + 
           tasks.length + colis.length;
  }
}
