import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import '../models/invoice.dart';
import '../models/product.dart';
import '../models/colis.dart';
import '../models/task.dart' as my_models;
import 'firebase_service.dart';
import 'logging_service.dart';

/// Service pour diagnostiquer et récupérer les anciennes données
class DataRecoveryService {
  static DataRecoveryService? _instance;
  static DataRecoveryService get instance => _instance ??= DataRecoveryService._internal();
  
  DataRecoveryService._internal();

  /// Diagnostiquer les problèmes de données
  Future<Map<String, dynamic>> diagnoseDat aProblems() async {
    final diagnosis = <String, dynamic>{};
    
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Vérifier les données locales
      final localData = await _checkLocalData(prefs);
      diagnosis['localData'] = localData;
      
      // Vérifier les données Firebase
      final firebaseData = await _checkFirebaseData();
      diagnosis['firebaseData'] = firebaseData;
      
      // Vérifier la synchronisation
      final syncStatus = await _checkSyncStatus(prefs);
      diagnosis['syncStatus'] = syncStatus;
      
      // Identifier les problèmes
      final problems = _identifyProblems(localData, firebaseData, syncStatus);
      diagnosis['problems'] = problems;
      
      // Proposer des solutions
      final solutions = _proposeSolutions(problems);
      diagnosis['solutions'] = solutions;
      
      LoggingService.info('Diagnostic des données terminé', 'DATA_RECOVERY');
      
    } catch (e) {
      LoggingService.error('Erreur lors du diagnostic', 'DATA_RECOVERY', e);
      diagnosis['error'] = e.toString();
    }
    
    return diagnosis;
  }
  
  /// Vérifier les données locales dans SharedPreferences
  Future<Map<String, dynamic>> _checkLocalData(SharedPreferences prefs) async {
    final localData = <String, dynamic>{};
    
    try {
      // Vérifier les factures
      final invoicesJson = prefs.getString('hcp_invoices');
      if (invoicesJson != null) {
        final invoicesList = jsonDecode(invoicesJson) as List;
        localData['invoices'] = {
          'count': invoicesList.length,
          'hasData': invoicesList.isNotEmpty,
          'lastModified': _getLastModifiedDate(invoicesList),
        };
      } else {
        // Vérifier l'ancien format
        final oldInvoices = prefs.getStringList('invoices') ?? [];
        localData['invoices'] = {
          'count': oldInvoices.length,
          'hasData': oldInvoices.isNotEmpty,
          'format': 'old',
        };
      }
      
      // Vérifier les colis
      final colisJson = prefs.getStringList('hcp_colis') ?? [];
      localData['colis'] = {
        'count': colisJson.length,
        'hasData': colisJson.isNotEmpty,
      };
      
      // Vérifier les produits
      final productsJson = prefs.getString('products');
      if (productsJson != null) {
        final productsList = jsonDecode(productsJson) as List;
        localData['products'] = {
          'count': productsList.length,
          'hasData': productsList.isNotEmpty,
        };
      }
      
      // Vérifier les tâches
      final tasksJson = prefs.getString('tasks');
      if (tasksJson != null) {
        final tasksList = jsonDecode(tasksJson) as List;
        localData['tasks'] = {
          'count': tasksList.length,
          'hasData': tasksList.isNotEmpty,
        };
      }
      
    } catch (e) {
      localData['error'] = e.toString();
    }
    
    return localData;
  }
  
  /// Vérifier les données Firebase
  Future<Map<String, dynamic>> _checkFirebaseData() async {
    final firebaseData = <String, dynamic>{};
    
    try {
      final firebaseService = FirebaseService.instance;
      
      if (!firebaseService.isOnline()) {
        firebaseData['status'] = 'offline';
        return firebaseData;
      }
      
      // Vérifier les factures Firebase
      final firebaseInvoices = await firebaseService.getAllInvoices();
      firebaseData['invoices'] = {
        'count': firebaseInvoices.length,
        'hasData': firebaseInvoices.isNotEmpty,
      };
      
      // Vérifier les colis Firebase
      final firebaseColis = await firebaseService.getAllColis();
      firebaseData['colis'] = {
        'count': firebaseColis.length,
        'hasData': firebaseColis.isNotEmpty,
      };
      
      // Vérifier les produits Firebase
      final firebaseProducts = await firebaseService.getAllProducts();
      firebaseData['products'] = {
        'count': firebaseProducts.length,
        'hasData': firebaseProducts.isNotEmpty,
      };
      
      // Vérifier les tâches Firebase
      final firebaseTasks = await firebaseService.getAllTasks();
      firebaseData['tasks'] = {
        'count': firebaseTasks.length,
        'hasData': firebaseTasks.isNotEmpty,
      };
      
      firebaseData['status'] = 'online';
      
    } catch (e) {
      firebaseData['error'] = e.toString();
      firebaseData['status'] = 'error';
    }
    
    return firebaseData;
  }
  
  /// Vérifier le statut de synchronisation
  Future<Map<String, dynamic>> _checkSyncStatus(SharedPreferences prefs) async {
    final syncStatus = <String, dynamic>{};
    
    try {
      // Vérifier si la migration est terminée
      final migrationComplete = prefs.getBool('unified_sync_migration_complete') ?? false;
      syncStatus['migrationComplete'] = migrationComplete;
      
      // Vérifier la dernière synchronisation
      final lastSyncString = prefs.getString('unified_sync_last_sync');
      if (lastSyncString != null) {
        final lastSync = DateTime.parse(lastSyncString);
        syncStatus['lastSync'] = lastSync.toIso8601String();
        syncStatus['daysSinceLastSync'] = DateTime.now().difference(lastSync).inDays;
      }
      
      // Vérifier les opérations en attente
      final pendingOps = prefs.getString('firebase_pending_operations');
      if (pendingOps != null) {
        final pendingList = jsonDecode(pendingOps) as List;
        syncStatus['pendingOperations'] = pendingList.length;
      } else {
        syncStatus['pendingOperations'] = 0;
      }
      
    } catch (e) {
      syncStatus['error'] = e.toString();
    }
    
    return syncStatus;
  }
  
  /// Identifier les problèmes
  List<String> _identifyProblems(Map<String, dynamic> localData, Map<String, dynamic> firebaseData, Map<String, dynamic> syncStatus) {
    final problems = <String>[];
    
    // Problème 1: Données locales mais pas de migration
    if (localData['invoices']?['hasData'] == true && syncStatus['migrationComplete'] != true) {
      problems.add('Migration incomplète: Des données locales existent mais n\'ont pas été migrées');
    }
    
    // Problème 2: Pas de données du tout
    if (localData['invoices']?['hasData'] != true && firebaseData['invoices']?['hasData'] != true) {
      problems.add('Aucune donnée trouvée: Ni en local ni sur Firebase');
    }
    
    // Problème 3: Données uniquement en local
    if (localData['invoices']?['hasData'] == true && firebaseData['invoices']?['hasData'] != true) {
      problems.add('Données uniquement locales: Les données ne sont pas synchronisées avec Firebase');
    }
    
    // Problème 4: Firebase hors ligne
    if (firebaseData['status'] == 'offline') {
      problems.add('Firebase hors ligne: Impossible d\'accéder aux données cloud');
    }
    
    // Problème 5: Synchronisation ancienne
    if (syncStatus['daysSinceLastSync'] != null && syncStatus['daysSinceLastSync'] > 7) {
      problems.add('Synchronisation ancienne: Dernière sync il y a ${syncStatus['daysSinceLastSync']} jours');
    }
    
    // Problème 6: Opérations en attente
    if (syncStatus['pendingOperations'] != null && syncStatus['pendingOperations'] > 0) {
      problems.add('Opérations en attente: ${syncStatus['pendingOperations']} opérations non synchronisées');
    }
    
    // Problème 7: Format de données obsolète
    if (localData['invoices']?['format'] == 'old') {
      problems.add('Format de données obsolète: Les données utilisent l\'ancien format');
    }
    
    return problems;
  }
  
  /// Proposer des solutions
  List<String> _proposeSolutions(List<String> problems) {
    final solutions = <String>[];
    
    for (final problem in problems) {
      if (problem.contains('Migration incomplète')) {
        solutions.add('Forcer une nouvelle migration complète');
      } else if (problem.contains('Aucune donnée')) {
        solutions.add('Initialiser avec des données de test ou importer une sauvegarde');
      } else if (problem.contains('Données uniquement locales')) {
        solutions.add('Lancer la synchronisation manuelle');
      } else if (problem.contains('Firebase hors ligne')) {
        solutions.add('Vérifier la connexion internet et réessayer');
      } else if (problem.contains('Synchronisation ancienne')) {
        solutions.add('Lancer une synchronisation manuelle');
      } else if (problem.contains('Opérations en attente')) {
        solutions.add('Forcer la synchronisation des opérations en attente');
      } else if (problem.contains('Format de données obsolète')) {
        solutions.add('Migrer les données vers le nouveau format');
      }
    }
    
    return solutions;
  }
  
  /// Récupérer les anciennes données
  Future<bool> recoverOldData() async {
    try {
      LoggingService.info('Début de la récupération des anciennes données', 'DATA_RECOVERY');
      
      final prefs = await SharedPreferences.getInstance();
      bool hasRecoveredData = false;
      
      // Récupérer les factures de l'ancien format
      final oldInvoices = prefs.getStringList('invoices') ?? [];
      if (oldInvoices.isNotEmpty) {
        LoggingService.info('Récupération de ${oldInvoices.length} factures anciennes', 'DATA_RECOVERY');
        
        final recoveredInvoices = <Invoice>[];
        for (final invoiceJson in oldInvoices) {
          try {
            final invoice = Invoice.fromJson(
              Map<String, dynamic>.from(
                Map.from(Uri.splitQueryString(invoiceJson)),
              ),
            );
            recoveredInvoices.add(invoice);
          } catch (e) {
            LoggingService.warning('Erreur récupération facture: $e', 'DATA_RECOVERY');
          }
        }
        
        if (recoveredInvoices.isNotEmpty) {
          // Sauvegarder dans le nouveau format
          final newInvoicesJson = jsonEncode(recoveredInvoices.map((i) => i.toJson()).toList());
          await prefs.setString('hcp_invoices', newInvoicesJson);
          hasRecoveredData = true;
          LoggingService.success('${recoveredInvoices.length} factures récupérées', 'DATA_RECOVERY');
        }
      }
      
      // Récupérer les colis de l'ancien format
      final oldColis = prefs.getStringList('colis') ?? [];
      if (oldColis.isNotEmpty) {
        LoggingService.info('Récupération de ${oldColis.length} colis anciens', 'DATA_RECOVERY');
        
        final recoveredColis = <String>[];
        for (final colisJson in oldColis) {
          try {
            // Vérifier que le JSON est valide
            final colis = Colis.fromJson(
              Map<String, dynamic>.from(
                Map.from(Uri.splitQueryString(colisJson)),
              ),
            );
            recoveredColis.add(jsonEncode(colis.toJson()));
          } catch (e) {
            LoggingService.warning('Erreur récupération colis: $e', 'DATA_RECOVERY');
          }
        }
        
        if (recoveredColis.isNotEmpty) {
          await prefs.setStringList('hcp_colis', recoveredColis);
          hasRecoveredData = true;
          LoggingService.success('${recoveredColis.length} colis récupérés', 'DATA_RECOVERY');
        }
      }
      
      // Marquer la récupération comme terminée
      if (hasRecoveredData) {
        await prefs.setBool('data_recovery_complete', true);
        LoggingService.success('Récupération des données terminée', 'DATA_RECOVERY');
      }
      
      return hasRecoveredData;
      
    } catch (e) {
      LoggingService.error('Erreur lors de la récupération', 'DATA_RECOVERY', e);
      return false;
    }
  }
  
  /// Forcer la resynchronisation
  Future<bool> forceSynchronization() async {
    try {
      LoggingService.info('Début de la resynchronisation forcée', 'DATA_RECOVERY');
      
      // Réinitialiser les flags de migration
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('unified_sync_migration_complete', false);
      
      // Forcer la synchronisation
      final firebaseService = FirebaseService.instance;
      await firebaseService.forceSyncNow();
      
      LoggingService.success('Resynchronisation forcée terminée', 'DATA_RECOVERY');
      return true;
      
    } catch (e) {
      LoggingService.error('Erreur lors de la resynchronisation', 'DATA_RECOVERY', e);
      return false;
    }
  }
  
  /// Obtenir la date de dernière modification
  String? _getLastModifiedDate(List<dynamic> dataList) {
    try {
      if (dataList.isEmpty) return null;
      
      DateTime? latestDate;
      for (final item in dataList) {
        if (item is Map<String, dynamic>) {
          final dateStr = item['dateCreation'] ?? item['dateAjout'] ?? item['createdAt'];
          if (dateStr != null) {
            final date = DateTime.parse(dateStr);
            if (latestDate == null || date.isAfter(latestDate)) {
              latestDate = date;
            }
          }
        }
      }
      
      return latestDate?.toIso8601String();
    } catch (e) {
      return null;
    }
  }
}