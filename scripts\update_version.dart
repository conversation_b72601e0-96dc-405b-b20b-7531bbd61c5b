import 'dart:io';
import 'dart:convert';

/// Script pour automatiser la mise à jour de version
void main(List<String> args) async {
  print('🚀 Script de mise à jour de version');
  
  if (args.isEmpty) {
    print('❌ Usage: dart run scripts/update_version.dart <version> [build_number]');
    print('   Exemple: dart run scripts/update_version.dart 2.1.0 3');
    exit(1);
  }

  final version = args[0];
  final buildNumber = args.length > 1 ? args[1] : null;
  
  try {
    await updatePubspecVersion(version, buildNumber);
    await updateVersionService(version, buildNumber);
    await generateChangelog(version);
    
    print('✅ Version mise à jour avec succès vers $version');
    print('📝 N\'oubliez pas de mettre à jour le changelog dans VersionService');
    print('🔄 Exécutez "flutter pub get" puis "flutter run" pour tester');
    
  } catch (e) {
    print('❌ Erreur lors de la mise à jour: $e');
    exit(1);
  }
}

/// Met à jour la version dans pubspec.yaml
Future<void> updatePubspecVersion(String version, String? buildNumber) async {
  final pubspecFile = File('pubspec.yaml');
  
  if (!await pubspecFile.exists()) {
    throw Exception('Fichier pubspec.yaml non trouvé');
  }
  
  final content = await pubspecFile.readAsString();
  final lines = content.split('\n');
  
  for (int i = 0; i < lines.length; i++) {
    if (lines[i].startsWith('version:')) {
      final currentVersion = lines[i].split(':')[1].trim();
      final currentParts = currentVersion.split('+');
      
      final newBuildNumber = buildNumber ?? 
          (currentParts.length > 1 ? 
              (int.parse(currentParts[1]) + 1).toString() : 
              '1');
      
      lines[i] = 'version: $version+$newBuildNumber';
      print('📝 Version mise à jour: $version+$newBuildNumber');
      break;
    }
  }
  
  await pubspecFile.writeAsString(lines.join('\n'));
}

/// Met à jour les valeurs par défaut dans VersionService
Future<void> updateVersionService(String version, String? buildNumber) async {
  final serviceFile = File('lib/services/version_service.dart');
  
  if (!await serviceFile.exists()) {
    print('⚠️  Fichier VersionService non trouvé, création...');
    return;
  }
  
  final content = await serviceFile.readAsString();
  final newBuildNumber = buildNumber ?? '1';
  
  // Mettre à jour les valeurs par défaut
  final updatedContent = content
      .replaceAll(
        RegExp(r"String get version => _packageInfo\?\.version \?\? '[^']*';"),
        "String get version => _packageInfo?.version ?? '$version';",
      )
      .replaceAll(
        RegExp(r"String get buildNumber => _packageInfo\?\.buildNumber \?\? '[^']*';"),
        "String get buildNumber => _packageInfo?.buildNumber ?? '$newBuildNumber';",
      );
  
  await serviceFile.writeAsString(updatedContent);
  print('📝 VersionService mis à jour');
}

/// Génère un template de changelog
Future<void> generateChangelog(String version) async {
  final changelogFile = File('CHANGELOG.md');
  final now = DateTime.now();
  final dateStr = '${now.day.toString().padLeft(2, '0')}/${now.month.toString().padLeft(2, '0')}/${now.year}';
  
  final newEntry = '''
## Version $version - $dateStr

### ✅ Nouvelles fonctionnalités
- [ ] Fonctionnalité 1
- [ ] Fonctionnalité 2

### 🔧 Améliorations
- [ ] Amélioration 1
- [ ] Amélioration 2

### 🐛 Corrections de bugs
- [ ] Correction 1
- [ ] Correction 2

### 🔄 Changements techniques
- [ ] Changement 1
- [ ] Changement 2

---

''';

  String existingContent = '';
  if (await changelogFile.exists()) {
    existingContent = await changelogFile.readAsString();
  } else {
    existingContent = '''# Changelog

Toutes les modifications notables de ce projet seront documentées dans ce fichier.

---

''';
  }
  
  // Insérer la nouvelle entrée après l'en-tête
  final lines = existingContent.split('\n');
  final headerEndIndex = lines.indexWhere((line) => line.startsWith('---'));
  
  if (headerEndIndex != -1) {
    lines.insert(headerEndIndex + 1, '');
    lines.insert(headerEndIndex + 2, newEntry);
  } else {
    lines.add(newEntry);
  }
  
  await changelogFile.writeAsString(lines.join('\n'));
  print('📝 Template de changelog généré dans CHANGELOG.md');
}
