import 'dart:math';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/invoice.dart';
import 'invoice_service.dart';

/// Service de gamification pour motiver les employés
class GamificationService {
  static final GamificationService _instance = GamificationService._internal();
  factory GamificationService() => _instance;
  GamificationService._internal();

  static GamificationService get instance => _instance;

  // Clés de stockage
  static const String _totalPointsKey = 'gamification_total_points';
  static const String _levelKey = 'gamification_level';
  static const String _badgesKey = 'gamification_badges';
  static const String _streakKey = 'gamification_streak';
  static const String _lastInvoiceDateKey = 'gamification_last_invoice_date';

  /// Obtenir les statistiques de gamification actuelles
  Future<GamificationStats> getGamificationStats() async {
    final prefs = await SharedPreferences.getInstance();
    
    final totalPoints = prefs.getInt(_totalPointsKey) ?? 0;
    final level = prefs.getInt(_levelKey) ?? 1;
    final badgesList = prefs.getStringList(_badgesKey) ?? [];
    final streak = prefs.getInt(_streakKey) ?? 0;
    final lastInvoiceDate = prefs.getString(_lastInvoiceDateKey);

    // Calculer les statistiques du mois en cours
    final monthlyStats = await _calculateMonthlyStats();

    return GamificationStats(
      totalPoints: totalPoints,
      level: level,
      badges: badgesList.map((id) => Badge.fromId(id)).toList(),
      currentStreak: streak,
      monthlyInvoices: monthlyStats['invoices'],
      monthlyRevenue: monthlyStats['revenue'],
      monthlyClients: monthlyStats['clients'],
      lastInvoiceDate: lastInvoiceDate != null ? DateTime.parse(lastInvoiceDate) : null,
    );
  }

  /// Traiter l'ajout d'une nouvelle facture et calculer les récompenses
  Future<GamificationReward> processNewInvoice(Invoice invoice) async {
    final prefs = await SharedPreferences.getInstance();
    
    // Points de base pour une facture
    int pointsEarned = 10;
    List<String> messages = ['🎉 Nouvelle commande enregistrée !'];
    List<Badge> newBadges = [];
    bool levelUp = false;
    
    // Bonus selon le montant de la facture
    final subtotal = InvoiceService().calculateSubtotal(invoice.items);
    if (subtotal >= 10000) {
      pointsEarned += 20;
      messages.add('💰 Grosse commande ! +20 points bonus');
    } else if (subtotal >= 5000) {
      pointsEarned += 10;
      messages.add('💵 Belle commande ! +10 points bonus');
    }

    // Bonus pour nouveau client
    if (await _isNewClient(invoice.clientName, invoice.clientNumber)) {
      pointsEarned += 15;
      messages.add('🆕 Nouveau client ! +15 points bonus');
    }

    // Mettre à jour les points totaux
    final currentPoints = prefs.getInt(_totalPointsKey) ?? 0;
    final newTotalPoints = currentPoints + pointsEarned;
    await prefs.setInt(_totalPointsKey, newTotalPoints);

    // Vérifier le niveau
    final currentLevel = prefs.getInt(_levelKey) ?? 1;
    final newLevel = _calculateLevel(newTotalPoints);
    if (newLevel > currentLevel) {
      await prefs.setInt(_levelKey, newLevel);
      levelUp = true;
      messages.add('🌟 NIVEAU $newLevel ATTEINT ! Félicitations !');
    }

    // Mettre à jour la série (streak)
    final newStreak = await _updateStreak();
    if (newStreak > 0 && newStreak % 5 == 0) {
      pointsEarned += newStreak;
      messages.add('🔥 Série de $newStreak jours ! +$newStreak points bonus');
    }

    // Vérifier les nouveaux badges
    newBadges = await _checkForNewBadges();
    if (newBadges.isNotEmpty) {
      for (final badge in newBadges) {
        messages.add('🏆 Badge débloqué : ${badge.name}');
      }
    }

    // Mettre à jour la date de dernière facture
    await prefs.setString(_lastInvoiceDateKey, DateTime.now().toIso8601String());

    return GamificationReward(
      pointsEarned: pointsEarned,
      messages: messages,
      newBadges: newBadges,
      levelUp: levelUp,
      newLevel: newLevel,
      currentStreak: newStreak,
    );
  }

  /// Calculer le niveau basé sur les points
  int _calculateLevel(int points) {
    // Niveau 1: 0-99 points
    // Niveau 2: 100-299 points
    // Niveau 3: 300-599 points
    // etc.
    if (points < 100) return 1;
    if (points < 300) return 2;
    if (points < 600) return 3;
    if (points < 1000) return 4;
    if (points < 1500) return 5;
    if (points < 2100) return 6;
    if (points < 2800) return 7;
    if (points < 3600) return 8;
    if (points < 4500) return 9;
    return 10; // Niveau maximum
  }

  /// Calculer les points nécessaires pour le prochain niveau
  int getPointsForNextLevel(int currentLevel) {
    switch (currentLevel) {
      case 1: return 100;
      case 2: return 300;
      case 3: return 600;
      case 4: return 1000;
      case 5: return 1500;
      case 6: return 2100;
      case 7: return 2800;
      case 8: return 3600;
      case 9: return 4500;
      default: return 4500; // Niveau max atteint
    }
  }

  /// Vérifier si c'est un nouveau client
  Future<bool> _isNewClient(String clientName, String clientNumber) async {
    final invoices = await InvoiceService.loadInvoices();
    final existingInvoices = invoices.where((inv) => 
      inv.clientName == clientName && inv.clientNumber == clientNumber
    ).toList();
    return existingInvoices.length <= 1; // Première facture pour ce client
  }

  /// Mettre à jour la série de jours consécutifs
  Future<int> _updateStreak() async {
    final prefs = await SharedPreferences.getInstance();
    final lastDateStr = prefs.getString(_lastInvoiceDateKey);
    final currentStreak = prefs.getInt(_streakKey) ?? 0;
    
    if (lastDateStr == null) {
      await prefs.setInt(_streakKey, 1);
      return 1;
    }

    final lastDate = DateTime.parse(lastDateStr);
    final today = DateTime.now();
    final daysDifference = today.difference(lastDate).inDays;

    int newStreak;
    if (daysDifference == 1) {
      // Jour consécutif
      newStreak = currentStreak + 1;
    } else if (daysDifference == 0) {
      // Même jour
      newStreak = currentStreak;
    } else {
      // Série cassée
      newStreak = 1;
    }

    await prefs.setInt(_streakKey, newStreak);
    return newStreak;
  }

  /// Vérifier les nouveaux badges débloqués
  Future<List<Badge>> _checkForNewBadges() async {
    final prefs = await SharedPreferences.getInstance();
    final currentBadges = prefs.getStringList(_badgesKey) ?? [];
    final stats = await getGamificationStats();
    final monthlyStats = await _calculateMonthlyStats();
    
    List<Badge> newBadges = [];
    
    // Badge première vente
    if (!currentBadges.contains('first_sale') && stats.totalPoints > 0) {
      newBadges.add(Badge.firstSale);
    }
    
    // Badge 10 factures
    if (!currentBadges.contains('ten_invoices') && monthlyStats['invoices'] >= 10) {
      newBadges.add(Badge.tenInvoices);
    }
    
    // Badge grosse vente
    if (!currentBadges.contains('big_sale')) {
      final invoices = await InvoiceService.loadInvoices();
      final hasBigSale = invoices.any((inv) => 
        InvoiceService().calculateSubtotal(inv.items) >= 20000
      );
      if (hasBigSale) {
        newBadges.add(Badge.bigSale);
      }
    }
    
    // Badge série de 7 jours
    if (!currentBadges.contains('week_streak') && stats.currentStreak >= 7) {
      newBadges.add(Badge.weekStreak);
    }
    
    // Badge niveau 5
    if (!currentBadges.contains('level_five') && stats.level >= 5) {
      newBadges.add(Badge.levelFive);
    }

    // Sauvegarder les nouveaux badges
    if (newBadges.isNotEmpty) {
      final updatedBadges = [...currentBadges, ...newBadges.map((b) => b.id)];
      await prefs.setStringList(_badgesKey, updatedBadges);
    }

    return newBadges;
  }

  /// Calculer les statistiques du mois en cours
  Future<Map<String, dynamic>> _calculateMonthlyStats() async {
    final invoices = await InvoiceService.loadInvoices();
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    
    final monthlyInvoices = invoices.where((inv) => 
      inv.createdAt.isAfter(startOfMonth) && inv.status == InvoiceStatus.payee
    ).toList();
    
    final revenue = monthlyInvoices.fold<double>(0, (sum, inv) => 
      sum + InvoiceService().calculateSubtotal(inv.items)
    );
    
    final uniqueClients = monthlyInvoices.map((inv) => 
      '${inv.clientName}_${inv.clientNumber}'
    ).toSet().length;
    
    return {
      'invoices': monthlyInvoices.length,
      'revenue': revenue,
      'clients': uniqueClients,
    };
  }

  /// Obtenir un message d'encouragement aléatoire
  String getRandomEncouragement() {
    final encouragements = [
      "💪 Continue comme ça !",
      "🚀 Tu es sur la bonne voie !",
      "⭐ Excellent travail !",
      "🎯 Objectif en vue !",
      "🔥 Tu assures !",
      "💎 Performance de champion !",
      "🌟 Brillant !",
      "🏆 Tu es le meilleur !",
      "💯 Performance parfaite !",
      "🎉 Fantastique !",
    ];
    return encouragements[Random().nextInt(encouragements.length)];
  }

  /// Réinitialiser les statistiques (pour les tests)
  Future<void> resetStats() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_totalPointsKey);
    await prefs.remove(_levelKey);
    await prefs.remove(_badgesKey);
    await prefs.remove(_streakKey);
    await prefs.remove(_lastInvoiceDateKey);
  }
}

/// Modèle pour les statistiques de gamification
class GamificationStats {
  final int totalPoints;
  final int level;
  final List<Badge> badges;
  final int currentStreak;
  final int monthlyInvoices;
  final double monthlyRevenue;
  final int monthlyClients;
  final DateTime? lastInvoiceDate;

  const GamificationStats({
    required this.totalPoints,
    required this.level,
    required this.badges,
    required this.currentStreak,
    required this.monthlyInvoices,
    required this.monthlyRevenue,
    required this.monthlyClients,
    this.lastInvoiceDate,
  });
}

/// Modèle pour les récompenses gagnées
class GamificationReward {
  final int pointsEarned;
  final List<String> messages;
  final List<Badge> newBadges;
  final bool levelUp;
  final int newLevel;
  final int currentStreak;

  const GamificationReward({
    required this.pointsEarned,
    required this.messages,
    required this.newBadges,
    required this.levelUp,
    required this.newLevel,
    required this.currentStreak,
  });
}

/// Modèle pour les badges
class Badge {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;

  const Badge({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
  });

  static const Badge firstSale = Badge(
    id: 'first_sale',
    name: 'Première Vente',
    description: 'Votre première facture enregistrée',
    icon: Icons.star,
    color: Colors.amber,
  );

  static const Badge tenInvoices = Badge(
    id: 'ten_invoices',
    name: 'Vendeur Actif',
    description: '10 factures ce mois-ci',
    icon: Icons.trending_up,
    color: Colors.green,
  );

  static const Badge bigSale = Badge(
    id: 'big_sale',
    name: 'Grosse Vente',
    description: 'Vente de plus de 20 000 FCFA',
    icon: Icons.diamond,
    color: Colors.purple,
  );

  static const Badge weekStreak = Badge(
    id: 'week_streak',
    name: 'Série Hebdomadaire',
    description: '7 jours consécutifs de ventes',
    icon: Icons.local_fire_department,
    color: Colors.orange,
  );

  static const Badge levelFive = Badge(
    id: 'level_five',
    name: 'Expert Vendeur',
    description: 'Niveau 5 atteint',
    icon: Icons.military_tech,
    color: Colors.blue,
  );

  static Badge fromId(String id) {
    switch (id) {
      case 'first_sale': return firstSale;
      case 'ten_invoices': return tenInvoices;
      case 'big_sale': return bigSale;
      case 'week_streak': return weekStreak;
      case 'level_five': return levelFive;
      default: return firstSale;
    }
  }
}
