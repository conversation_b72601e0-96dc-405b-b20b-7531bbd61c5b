import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../services/invoice_service.dart';
import '../services/inventory_service.dart';
import '../services/out_of_stock_service.dart';
import '../services/task_service.dart';
import '../services/colis_service.dart';
import '../services/notification_retours_service.dart';
import '../services/hybrid_supabase_service.dart';

import '../models/colis.dart';
import '../utils/performance_optimizer.dart';
import '../widgets/unified_performance_widget.dart';
import '../widgets/challenges_widget.dart';
import '../widgets/reorderable_grid_view.dart';
import '../widgets/dashboard_reorder_controls.dart';

import '../services/dashboard_preferences_service.dart';

import 'invoice_list_page.dart';
import 'product_list_page.dart';
import 'stock_alerts_page.dart';
import 'out_of_stock_products_page.dart';
import 'most_sold_out_of_stock_page.dart';
import 'backup_restore_page.dart';
import 'notification_settings_page.dart';
import 'local_sync_page.dart';
import 'category_management_page.dart';
import 'whatsapp_media_demo_page.dart';
import 'security_settings_page.dart';
import 'tasks_page.dart';
import 'retours_page.dart';
import 'ajouter_livraison_page.dart';
import 'commandes_jour_page.dart';
import 'mini_facture_test_page.dart';
import 'sync_diagnostic_page.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage>
    with TickerProviderStateMixin, PerformanceOptimizedState {
  Map<String, dynamic> _invoiceStats = {};
  Map<String, dynamic> _inventoryStats = {};
  Map<String, dynamic> _outOfStockStats = {};
  Map<String, dynamic> _livraisonStats = {};
  bool _isLoading = true;
  final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');
  final InvoiceService _invoiceService = InvoiceService();
  final ColisService _colisService = ColisService();
  final NotificationRetoursService _notificationService =
      NotificationRetoursService.instance;
  int _pendingTasks = 0;
  int _pendingReturns = 0;

  // Listes pour stocker l'ordre des cartes dans chaque section
  List<Widget> _invoiceCards = [];
  List<Widget> _inventoryCards = [];
  List<Widget> _outOfStockCards = [];
  List<Widget> _mainCards = []; // Cartes principales réorganisables

  // Variables pour la réorganisation
  bool _isReorderMode = false;
  final DashboardPreferencesService _preferencesService =
      DashboardPreferencesService.instance;

  // Contrôleurs d'animation
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _staggerController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Objectif mensuel (peut être configuré)
  final double _monthlyTarget = 1000000; // 1 million FCFA

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadAllStats();
    _loadPendingTasks();
    _loadPendingReturns();
    _initializeNotificationService();
    _initializeCardLists();
    _loadPreferences();
  }

  // Initialiser les listes de cartes
  void _initializeCardLists() {
    // Les listes seront initialisées après le chargement des données
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Initialiser les cartes une fois que le contexte est disponible
    if (!_isLoading) {
      _invoiceCards = _createInvoiceCards(context);
      _inventoryCards = _createInventoryCards(context);
      _outOfStockCards = _createOutOfStockCards(context);
      _mainCards = _createMainCards(context);
    }
  }

  // Méthode pour créer les cartes de factures
  List<Widget> _createInvoiceCards(BuildContext context) {
    if (_invoiceCards.isEmpty && !_isLoading) {
      _invoiceCards = [
        _buildStatCard(
          'Total Factures',
          '${_invoiceStats['totalInvoices'] ?? 0}',
          Icons.receipt_long,
          Colors.blue,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const InvoiceListPage()),
            );
          },
        ),
        _buildStatCard(
          'En Attente',
          '${_invoiceStats['pendingInvoices'] ?? 0}',
          Icons.pending,
          Colors.orange,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const InvoiceListPage()),
            );
          },
        ),
        _buildStatCard(
          'Livrées',
          '${_invoiceStats['deliveredInvoices'] ?? 0}',
          Icons.check_circle,
          Colors.green,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const InvoiceListPage()),
            );
          },
        ),
        _buildStatCard(
          'Chiffre d\'Affaires',
          '${_currencyFormat.format(_invoiceStats['totalRevenue'] ?? 0)} FCFA',
          Icons.monetization_on,
          Colors.purple,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const InvoiceListPage()),
            );
          },
        ),
      ];
    }
    return _invoiceCards;
  }

  // Méthode pour créer les cartes d'inventaire
  List<Widget> _createInventoryCards(BuildContext context) {
    if (_inventoryCards.isEmpty && !_isLoading) {
      _inventoryCards = [
        _buildStatCard(
          'Total Produits',
          '${_inventoryStats['totalProducts'] ?? 0}',
          Icons.inventory_2,
          Colors.blue,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const ProductListPage()),
            );
          },
        ),
        _buildStatCard(
          'Stock Bas',
          '${_inventoryStats['lowStockProducts'] ?? 0}',
          Icons.warning,
          Colors.orange,
          subtitle: 'Seuil ≤ 5',
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const StockAlertsPage()),
            );
          },
        ),
        _buildStatCard(
          'Rupture Stock',
          '${_inventoryStats['outOfStockProducts'] ?? 0}',
          Icons.error,
          Colors.red,
          subtitle: 'Quantité = 0',
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const StockAlertsPage()),
            );
          },
        ),
        _buildStatCard(
          'Valeur Totale',
          '${_currencyFormat.format(_inventoryStats['totalInventoryValue'] ?? 0)} FCFA',
          Icons.account_balance_wallet,
          Colors.green,
          subtitle: 'Inventaire',
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const ProductListPage()),
            );
          },
        ),
        _buildStatCard(
          'Catégories',
          'Gérer',
          Icons.category,
          Colors.purple,
          subtitle: 'Modifier/Supprimer',
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const CategoryManagementPage(),
              ),
            );
          },
        ),
      ];
    }
    return _inventoryCards;
  }

  // Méthode pour créer les cartes de produits hors stock
  List<Widget> _createOutOfStockCards(BuildContext context) {
    if (_outOfStockCards.isEmpty && !_isLoading) {
      _outOfStockCards = [
        _buildStatCard(
          'Produits Total H-S',
          '${_outOfStockStats['totalOutOfStockProducts'] ?? 0}',
          Icons.inventory_2_outlined,
          Colors.red,
          subtitle: 'Ce mois',
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const OutOfStockProductsPage(),
              ),
            );
          },
        ),
        _buildStatCard(
          'Produit le Plus Vendu',
          _outOfStockStats['mostSoldProduct']?.productName ?? 'Aucun',
          Icons.star,
          Colors.orange,
          subtitle: 'H-S',
          onTap: () {
            if (_outOfStockStats['mostSoldProduct'] != null) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MostSoldOutOfStockPage(),
                ),
              );
            }
          },
        ),
      ];
    }
    return _outOfStockCards;
  }

  // Méthode pour créer les cartes principales réorganisables
  List<Widget> _createMainCards(BuildContext context) {
    if (_mainCards.isEmpty && !_isLoading) {
      _mainCards = [_buildObjectifCard(), _buildLivraisonCard()];
    }
    return _mainCards;
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _staggerController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );
  }

  void _startAnimations() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _slideController.forward();
    });
    Future.delayed(const Duration(milliseconds: 500), () {
      _staggerController.forward();
    });
  }

  Future<void> _loadAllStats() async {
    safeSetState(() => _isLoading = true);
    try {
      // Initialiser le service hybride d'abord
      await HybridSupabaseService.initialize();

      // Charger toutes les données en parallèle pour améliorer les performances
      final results = await Future.wait([
        _invoiceService.getInvoiceStats(),
        InventoryService.instance.getProducts(),
        OutOfStockService.getOutOfStockStats(),
        _colisService.getDashboardData(),
      ]);

      final invoiceStats = results[0] as Map<String, dynamic>;
      final products = results[1] as List;
      final outOfStockStats = results[2] as Map<String, dynamic>;
      final livraisonStats = results[3] as Map<String, dynamic>;

      // Calculer les statistiques d'inventaire
      final totalProducts = products.length;
      final lowStockProducts = products.where((p) => p.quantity <= 5).length;
      final outOfStockProducts = products.where((p) => p.quantity == 0).length;
      final totalInventoryValue = products.fold<double>(
        0.0,
        (sum, product) => sum + (product.price * product.quantity),
      );

      // Un seul setState pour toutes les données
      safeSetState(() {
        _invoiceStats = invoiceStats;
        _inventoryStats = {
          'totalProducts': totalProducts,
          'lowStockProducts': lowStockProducts,
          'outOfStockProducts': outOfStockProducts,
          'totalInventoryValue': totalInventoryValue,
        };
        _outOfStockStats = outOfStockStats;
        _livraisonStats = livraisonStats;
        _isLoading = false;
      });

      // Démarrer les animations après le chargement des données avec délai
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) _startAnimations();
      });
    } catch (e) {
      safeSetState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Erreur de chargement: $e')));
      }
    }
  }

  Future<void> _loadPendingTasks() async {
    final pending = await TaskService.instance.getPendingTasks();
    setState(() {
      _pendingTasks = pending.length;
    });
  }

  Future<void> _loadPendingReturns() async {
    final returns = await _colisService.obtenirColisParStatut(
      StatutLivraison.retour,
    );
    setState(() {
      _pendingReturns = returns.length;
    });
  }

  Future<void> _initializeNotificationService() async {
    // Configurer le callback pour mettre à jour le compteur de retours
    _notificationService.onRetoursCountChanged = (count) {
      if (mounted) {
        setState(() {
          _pendingReturns = count;
        });
      }
    };

    // Démarrer les notifications périodiques
    await _notificationService.startPeriodicNotifications();
  }

  // Méthodes de gestion des préférences de réorganisation
  Future<void> _loadPreferences() async {
    try {
      final reorderMode = await _preferencesService.getReorderMode();

      safeSetState(() {
        _isReorderMode = reorderMode;
      });
    } catch (e) {
      // En cas d'erreur, utiliser les valeurs par défaut
      safeSetState(() {
        _isReorderMode = false;
      });
    }
  }

  void _toggleReorderMode() async {
    final newMode = !_isReorderMode;
    await _preferencesService.setReorderMode(newMode);

    safeSetState(() {
      _isReorderMode = newMode;
    });

    if (mounted) {
      if (newMode) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Mode réorganisation activé - Glissez les cartes pour les réorganiser',
            ),
            backgroundColor: Colors.blue,
            duration: Duration(seconds: 3),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Ordre des cartes sauvegardé'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  void _resetCardOrder() async {
    // Pour cette version simplifiée, on remet juste les cartes dans leur ordre initial
    _initializeCardLists();
    // Réinitialiser aussi les cartes principales
    _mainCards = _createMainCards(context);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Ordre des cartes réinitialisé'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _reorderMainCards(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final Widget item = _mainCards.removeAt(oldIndex);
      _mainCards.insert(newIndex, item);
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _staggerController.dispose();
    _notificationService.dispose();
    super.dispose();
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
    VoidCallback? onTap,
  }) {
    return Card(
      elevation: 4,
      color: color,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icône
              Icon(icon, size: 24, color: Colors.white),
              const SizedBox(height: 6),

              // Valeur principale - avec FittedBox pour éviter les débordements
              Flexible(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    value,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                  ),
                ),
              ),

              const SizedBox(height: 4),

              // Titre - avec gestion du débordement
              Flexible(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 10,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              // Sous-titre optionnel
              if (subtitle != null) ...[
                const SizedBox(height: 2),
                Flexible(
                  child: Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 8,
                      color: Colors.white.withValues(alpha: 0.8),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border(left: BorderSide(width: 4, color: Colors.blue[600]!)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue[600],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: Colors.white, size: 20),
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: Colors.grey[800],
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'HCP-DESIGN Analytics',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.sync),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const LocalSyncPage()),
              );
            },
            tooltip: 'Synchronisation Locale',
          ),
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const NotificationSettingsPage(),
                ),
              );
            },
            tooltip: 'Paramètres de Notification',
          ),
          IconButton(
            icon: const Icon(Icons.backup),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const BackupRestorePage(),
                ),
              );
            },
            tooltip: 'Sauvegarde & Restauration',
          ),
          IconButton(
            icon: const Icon(Icons.security),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SecuritySettingsPage(),
                ),
              );
            },
            tooltip: 'Paramètres de Sécurité',
          ),
          IconButton(
            icon: const Icon(Icons.perm_media),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const WhatsAppMediaDemoPage(),
                ),
              );
            },
            tooltip: 'Démo Médias WhatsApp',
          ),
          IconButton(
            icon: const Icon(Icons.sync),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SyncDiagnosticPage(),
                ),
              );
            },
            tooltip: 'Diagnostic de Synchronisation',
          ),
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadAllStats),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: RefreshIndicator(
                    onRefresh: () async {
                      await _loadAllStats();
                      await _loadPendingTasks();
                    },
                    child: ListView(
                      padding: const EdgeInsets.all(16),
                      children: [
                        // Contrôles de réorganisation
                        DashboardReorderControls(
                          isReorderMode: _isReorderMode,
                          onToggleReorderMode: _toggleReorderMode,
                          onResetOrder: _resetCardOrder,
                        ),
                        // Cartes principales réorganisables
                        if (_mainCards.isEmpty)
                          const Center(child: CircularProgressIndicator())
                        else if (_isReorderMode)
                          // Mode réorganisation : utiliser ReorderableListView
                          ReorderableListView(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            onReorder: _reorderMainCards,
                            children:
                                _mainCards.asMap().entries.map((entry) {
                                  final index = entry.key;
                                  final card = entry.value;
                                  return Container(
                                    key: ValueKey('main_card_$index'),
                                    margin: const EdgeInsets.only(bottom: 16),
                                    child: Stack(
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(
                                              16,
                                            ),
                                            border: Border.all(
                                              color: Colors.blue.withValues(
                                                alpha: 0.5,
                                              ),
                                              width: 3,
                                            ),
                                          ),
                                          child: card,
                                        ),
                                        // Icône de déplacement
                                        Positioned(
                                          top: 8,
                                          left: 8,
                                          child: Container(
                                            padding: const EdgeInsets.all(8),
                                            decoration: BoxDecoration(
                                              color: Colors.blue.withValues(
                                                alpha: 0.9,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                            ),
                                            child: const Icon(
                                              Icons.drag_handle,
                                              color: Colors.white,
                                              size: 20,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                }).toList(),
                          )
                        else
                          // Mode normal : affichage simple
                          ..._mainCards.asMap().entries.map((entry) {
                            final card = entry.value;
                            return Container(
                              margin: const EdgeInsets.only(bottom: 16),
                              child: card,
                            );
                          }),

                        // Communes les plus sollicitées
                        _buildSectionHeader(
                          'COMMUNES SOLLICITÉES',
                          Icons.location_city,
                        ),

                        // Carte des communes les plus sollicitées
                        Card(
                          elevation: 4,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Top 4 des communes de livraison ce mois-ci',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                if ((_invoiceStats['topDeliveryLocations']
                                            as List?)
                                        ?.isEmpty ??
                                    true)
                                  const Center(
                                    child: Padding(
                                      padding: EdgeInsets.all(16.0),
                                      child: Text(
                                        'Aucune livraison ce mois-ci',
                                        style: TextStyle(
                                          fontStyle: FontStyle.italic,
                                        ),
                                      ),
                                    ),
                                  )
                                else
                                  ...((_invoiceStats['topDeliveryLocations']
                                              as List?) ??
                                          [])
                                      .asMap()
                                      .entries
                                      .map((entry) {
                                        final index = entry.key;
                                        final location =
                                            entry.value['location'] as String;
                                        final count =
                                            entry.value['count'] as int;

                                        // Couleurs différentes selon le rang
                                        final colors = [
                                          Colors.amber, // Or pour le 1er
                                          Colors
                                              .blueGrey[300]!, // Argent pour le 2ème
                                          Colors
                                              .brown[300]!, // Bronze pour le 3ème
                                          Colors
                                              .grey[400]!, // Gris pour le 4ème
                                        ];

                                        return Padding(
                                          padding: const EdgeInsets.only(
                                            bottom: 12.0,
                                          ),
                                          child: Row(
                                            children: [
                                              Container(
                                                width: 30,
                                                height: 30,
                                                decoration: BoxDecoration(
                                                  color: colors[index],
                                                  shape: BoxShape.circle,
                                                ),
                                                child: Center(
                                                  child: Text(
                                                    '${index + 1}',
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(width: 12),
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      location,
                                                      style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        fontSize: 16,
                                                      ),
                                                    ),
                                                    Text(
                                                      '$count livraison${count > 1 ? 's' : ''}',
                                                      style: TextStyle(
                                                        color: Colors.grey[600],
                                                        fontSize: 14,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      }),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Section Performance & Gamification Unifiée
                        _buildSectionHeader(
                          'PERFORMANCE & OBJECTIFS',
                          Icons.emoji_events,
                        ),

                        // Widget unifié de performance et gamification
                        const UnifiedPerformanceWidget(),

                        const SizedBox(height: 16),

                        // Section Défis Temporaires
                        _buildSectionHeader('DÉFIS TEMPORAIRES', Icons.flag),

                        // Widget des défis
                        const ChallengesWidget(),

                        const SizedBox(height: 16),

                        // Section Factures
                        _buildSectionHeader(
                          'STATISTIQUES FACTURES',
                          Icons.receipt_long,
                        ),

                        // Les cartes de factures sont créées dans _initializeCardLists
                        ReorderableGridView(
                          forcedCrossAxisCount: 3,
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          mainAxisSpacing: 12,
                          crossAxisSpacing: 12,
                          childAspectRatio: 0.85,
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          onReorder:
                              _isReorderMode
                                  ? (oldIndex, newIndex) {
                                    setState(() {
                                      final Widget item = _invoiceCards
                                          .removeAt(oldIndex);
                                      _invoiceCards.insert(newIndex, item);
                                    });
                                  }
                                  : (
                                    oldIndex,
                                    newIndex,
                                  ) {}, // Désactivé si pas en mode réorganisation
                          children:
                              _invoiceCards.isEmpty
                                  ? [
                                    // Afficher un indicateur de chargement si les cartes ne sont pas encore créées
                                    const Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                  ]
                                  : _invoiceCards
                                      .map(
                                        (card) =>
                                            _isReorderMode
                                                ? Container(
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          12,
                                                        ),
                                                    border: Border.all(
                                                      color: Colors.blue
                                                          .withValues(
                                                            alpha: 0.3,
                                                          ),
                                                      width: 2,
                                                    ),
                                                  ),
                                                  child: card,
                                                )
                                                : card,
                                      )
                                      .toList(),
                        ),

                        // Section Inventaire
                        _buildSectionHeader(
                          'STATISTIQUES INVENTAIRE',
                          Icons.inventory,
                        ),

                        // Les cartes d'inventaire sont créées dans _initializeCardLists
                        ReorderableGridView(
                          forcedCrossAxisCount: 2,
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          mainAxisSpacing: 12,
                          crossAxisSpacing: 12,
                          childAspectRatio: 1.0,
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          onReorder:
                              _isReorderMode
                                  ? (oldIndex, newIndex) {
                                    setState(() {
                                      final Widget item = _inventoryCards
                                          .removeAt(oldIndex);
                                      _inventoryCards.insert(newIndex, item);
                                    });
                                  }
                                  : (oldIndex, newIndex) {},
                          children:
                              _inventoryCards.isEmpty
                                  ? [
                                    // Afficher un indicateur de chargement si les cartes ne sont pas encore créées
                                    const Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                  ]
                                  : _inventoryCards
                                      .map(
                                        (card) =>
                                            _isReorderMode
                                                ? Container(
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          12,
                                                        ),
                                                    border: Border.all(
                                                      color: Colors.blue
                                                          .withValues(
                                                            alpha: 0.3,
                                                          ),
                                                      width: 2,
                                                    ),
                                                  ),
                                                  child: card,
                                                )
                                                : card,
                                      )
                                      .toList(),
                        ),

                        // Section Produits Hors Stock
                        _buildSectionHeader(
                          'STATISTIQUES PRODUITS H-S',
                          Icons.warning_amber,
                        ),

                        // Les cartes de produits hors stock sont créées dans _initializeCardLists
                        ReorderableGridView(
                          forcedCrossAxisCount: 2,
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          mainAxisSpacing: 12,
                          crossAxisSpacing: 12,
                          childAspectRatio: 1.0,
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          onReorder:
                              _isReorderMode
                                  ? (oldIndex, newIndex) {
                                    setState(() {
                                      final Widget item = _outOfStockCards
                                          .removeAt(oldIndex);
                                      _outOfStockCards.insert(newIndex, item);
                                    });
                                  }
                                  : (oldIndex, newIndex) {},
                          children:
                              _outOfStockCards.isEmpty
                                  ? [
                                    // Afficher un indicateur de chargement si les cartes ne sont pas encore créées
                                    const Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                  ]
                                  : _outOfStockCards
                                      .map(
                                        (card) =>
                                            _isReorderMode
                                                ? Container(
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          12,
                                                        ),
                                                    border: Border.all(
                                                      color: Colors.blue
                                                          .withValues(
                                                            alpha: 0.3,
                                                          ),
                                                      width: 2,
                                                    ),
                                                  ),
                                                  child: card,
                                                )
                                                : card,
                                      )
                                      .toList(),
                        ),

                        const SizedBox(height: 24),

                        // Section Mini Facture
                        _buildSectionHeader('MINI FACTURE 5x3"', Icons.receipt),

                        Card(
                          elevation: 4,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: InkWell(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) => const MiniFactureTestPage(),
                                ),
                              );
                            },
                            borderRadius: BorderRadius.circular(12),
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.purple[400]!,
                                    Colors.purple[600]!,
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withValues(
                                        alpha: 0.2,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Icon(
                                      Icons.picture_as_pdf,
                                      color: Colors.white,
                                      size: 32,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Text(
                                          'Générer Mini Facture',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        const Text(
                                          'Format 5x3 pouces pour imprimantes thermiques',
                                          style: TextStyle(
                                            color: Colors.white70,
                                            fontSize: 14,
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 4,
                                          ),
                                          decoration: BoxDecoration(
                                            color: Colors.white.withValues(
                                              alpha: 0.2,
                                            ),
                                            borderRadius: BorderRadius.circular(
                                              4,
                                            ),
                                          ),
                                          child: const Text(
                                            'QR Codes inclus',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const Icon(
                                    Icons.arrow_forward_ios,
                                    color: Colors.white70,
                                    size: 20,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Alertes et notifications
                        if ((_inventoryStats['lowStockProducts'] ?? 0) > 0 ||
                            (_inventoryStats['outOfStockProducts'] ?? 0) >
                                0) ...[
                          Card(
                            color: Colors.orange[50],
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.warning,
                                        color: Colors.orange[700],
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'ALERTES STOCK',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.orange[700],
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  if ((_inventoryStats['outOfStockProducts'] ??
                                          0) >
                                      0)
                                    Text(
                                      '• ${_inventoryStats['outOfStockProducts']} produit(s) en rupture de stock',
                                      style: const TextStyle(color: Colors.red),
                                    ),
                                  if ((_inventoryStats['lowStockProducts'] ??
                                          0) >
                                      0)
                                    Text(
                                      '• ${_inventoryStats['lowStockProducts']} produit(s) avec stock bas',
                                      style: TextStyle(
                                        color: Colors.orange[700],
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AjouterLivraisonPage(),
            ),
          ).then((_) => _loadAllStats());
        },
        icon: const Icon(Icons.local_shipping),
        label: const Text('+ Livraison'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  Widget _buildObjectifCard() {
    return Stack(
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.green[900]!, Colors.green[700]!],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.track_changes, color: Colors.white, size: 28),
                  const SizedBox(width: 12),
                  const Text(
                    'Objectif du mois',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Montant actuel vs objectif
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Réalisé',
                        style: TextStyle(
                          color: Colors.green[100],
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '${_currencyFormat.format(_invoiceStats['totalRevenue'] ?? 0)} FCFA',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'Objectif',
                        style: TextStyle(
                          color: Colors.green[100],
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '${_currencyFormat.format(_monthlyTarget)} FCFA',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Jauge de progression
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Progression',
                        style: TextStyle(
                          color: Colors.green[100],
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '${((_invoiceStats['totalRevenue'] ?? 0) / _monthlyTarget * 100).toStringAsFixed(1)}%',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Container(
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.green[800],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: LinearProgressIndicator(
                        value: ((_invoiceStats['totalRevenue'] ?? 0) /
                                _monthlyTarget)
                            .clamp(0.0, 1.0),
                        backgroundColor: Colors.transparent,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          ((_invoiceStats['totalRevenue'] ?? 0) /
                                      _monthlyTarget) >=
                                  1.0
                              ? Colors.yellow[400]!
                              : Colors.white,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    ((_invoiceStats['totalRevenue'] ?? 0) / _monthlyTarget) >=
                            1.0
                        ? '🎉 Objectif atteint ! Félicitations !'
                        : 'Reste ${_currencyFormat.format(_monthlyTarget - (_invoiceStats['totalRevenue'] ?? 0))} FCFA',
                    style: TextStyle(
                      color:
                          ((_invoiceStats['totalRevenue'] ?? 0) /
                                      _monthlyTarget) >=
                                  1.0
                              ? Colors.yellow[300]
                              : Colors.green[100],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        if (_pendingReturns > 0)
          Positioned(
            top: 12,
            right: _pendingTasks > 0 ? 70 : 16,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(builder: (context) => const RetoursPage()),
                );
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                alignment: Alignment.center,
                child: Text(
                  '$_pendingReturns',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w900,
                    fontSize: 28,
                  ),
                ),
              ),
            ),
          ),
        if (_pendingTasks > 0)
          Positioned(
            top: 12,
            right: 16,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(builder: (context) => const TasksPage()),
                );
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
                alignment: Alignment.center,
                child: Text(
                  '$_pendingTasks',
                  style: const TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.w900,
                    fontSize: 28,
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildLivraisonCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.blue[600]!, Colors.blue[800]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.local_shipping, color: Colors.white, size: 28),
                  const SizedBox(width: 16),
                  const Text(
                    'Livraisons du jour',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  InkWell(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const CommandesJourPage(),
                        ),
                      ).then((_) => _loadAllStats());
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        'Voir tout',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Dernière livraison
              if (_livraisonStats['lastDeliveryDate'] != null) ...[
                Row(
                  children: [
                    Icon(Icons.schedule, color: Colors.white70, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Dernière livraison: ${_livraisonStats['lastDeliveryDate']}',
                      style: TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],

              // Montants par zone de livraison
              if (_livraisonStats['amountsByZone'] != null &&
                  (_livraisonStats['amountsByZone'] as Map).isNotEmpty) ...[
                Text(
                  'Montants par zone:',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                ...(_livraisonStats['amountsByZone'] as Map).entries.map(
                  (entry) => Padding(
                    padding: const EdgeInsets.only(bottom: 2),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          entry.key,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          '${_currencyFormat.format(entry.value)} FCFA',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 12),
              ],

              // Coût total et bénéfice
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Coût livraison',
                          style: TextStyle(color: Colors.white70, fontSize: 12),
                        ),
                        Text(
                          '${_currencyFormat.format(_livraisonStats['totalDailyCost'] ?? 0)} FCFA',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          'Bénéfice du jour',
                          style: TextStyle(color: Colors.white70, fontSize: 12),
                        ),
                        Text(
                          '${_currencyFormat.format(_livraisonStats['dailyProfit'] ?? 0)} FCFA',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
