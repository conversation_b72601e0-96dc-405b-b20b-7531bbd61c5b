import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../services/challenges_service.dart';

class ChallengesWidget extends StatefulWidget {
  const ChallengesWidget({super.key});

  @override
  State<ChallengesWidget> createState() => _ChallengesWidgetState();
}

class _ChallengesWidgetState extends State<ChallengesWidget>
    with TickerProviderStateMixin {
  List<ChallengeProgress> _challengeProgress = [];
  bool _isLoading = true;
  final ChallengesService _challengesService = ChallengesService.instance;
  final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');

  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadChallenges();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
  }

  Future<void> _loadChallenges() async {
    setState(() => _isLoading = true);
    try {
      // Créer des défis automatiques si nécessaire
      await _challengesService.createAutomaticChallenges();
      
      // Charger la progression des défis
      final progress = await _challengesService.checkChallengeProgress();
      
      setState(() {
        _challengeProgress = progress;
        _isLoading = false;
      });

      // Démarrer l'animation
      _slideController.forward();
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors du chargement des défis: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    if (_challengeProgress.isEmpty) {
      return Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [Colors.grey[300]!, Colors.grey[400]!],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            children: [
              Icon(Icons.emoji_events_outlined, size: 48, color: Colors.grey[600]),
              const SizedBox(height: 12),
              Text(
                'Aucun défi actif',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Les nouveaux défis arrivent bientôt !',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return SlideTransition(
      position: _slideAnimation,
      child: Card(
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              colors: [
                Colors.deepOrange[400]!,
                Colors.red[500]!,
                Colors.pink[400]!,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: 20),
                ..._challengeProgress.map((progress) => 
                  _buildChallengeCard(progress)
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(Icons.flag, color: Colors.white, size: 28),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Défis Temporaires',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Text(
                '${_challengeProgress.length} défi${_challengeProgress.length > 1 ? 's' : ''} actif${_challengeProgress.length > 1 ? 's' : ''}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ),
            ],
          ),
        ),
        IconButton(
          icon: const Icon(Icons.refresh, color: Colors.white),
          onPressed: _loadChallenges,
        ),
      ],
    );
  }

  Widget _buildChallengeCard(ChallengeProgress progress) {
    final challenge = progress.challenge;
    final daysLeft = challenge.endDate.difference(DateTime.now()).inDays;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: progress.isCompleted 
              ? Colors.green.withValues(alpha: 0.5)
              : Colors.white.withValues(alpha: 0.3),
          width: progress.isCompleted ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // En-tête du défi
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: challenge.color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  challenge.icon,
                  color: challenge.color,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      challenge.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      challenge.description,
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              if (progress.isCompleted)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'COMPLÉTÉ',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Progression
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatChallengeValue(challenge.type, progress.currentValue),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '/ ${_formatChallengeValue(challenge.type, challenge.target)}',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 14,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Barre de progression
          LinearProgressIndicator(
            value: progress.progress,
            backgroundColor: Colors.white.withValues(alpha: 0.3),
            valueColor: AlwaysStoppedAnimation<Color>(
              progress.isCompleted ? Colors.green : challenge.color,
            ),
            minHeight: 6,
          ),
          
          const SizedBox(height: 8),
          
          // Informations supplémentaires
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${(progress.progress * 100).toStringAsFixed(1)}% complété',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 12,
                ),
              ),
              Text(
                daysLeft > 0 
                    ? '$daysLeft jour${daysLeft > 1 ? 's' : ''} restant${daysLeft > 1 ? 's' : ''}'
                    : 'Expiré',
                style: TextStyle(
                  color: daysLeft > 0 
                      ? Colors.white.withValues(alpha: 0.8)
                      : Colors.red[300],
                  fontSize: 12,
                  fontWeight: daysLeft <= 0 ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
          
          // Récompenses
          if (challenge.reward.physicalReward != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.amber[300]!.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.amber[300]!.withValues(alpha: 0.5)),
              ),
              child: Row(
                children: [
                  Icon(Icons.card_giftcard, color: Colors.amber[300], size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '🎁 ${challenge.reward.physicalReward}',
                      style: TextStyle(
                        color: Colors.amber[300],
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Text(
                    '+${challenge.reward.points} pts',
                    style: TextStyle(
                      color: Colors.amber[300],
                      fontSize: 11,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatChallengeValue(ChallengeType type, double value) {
    switch (type) {
      case ChallengeType.invoiceCount:
        return value.toInt().toString();
      case ChallengeType.revenue:
        return _currencyFormat.format(value);
      case ChallengeType.newClients:
        return value.toInt().toString();
      case ChallengeType.consistency:
        return value.toInt().toString();
    }
  }
}
