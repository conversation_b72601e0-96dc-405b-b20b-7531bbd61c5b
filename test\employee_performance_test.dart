import 'package:flutter_test/flutter_test.dart';
import 'package:general_hcp_crm/services/employee_performance_service.dart';

void main() {
  group('EmployeePerformanceService Tests', () {
    late EmployeePerformanceService service;

    setUp(() {
      service = EmployeePerformanceService.instance;
    });

    test('calculateSalary should return correct salary for different client counts', () {
      // Test avec 0 clients
      expect(service.calculateSalary(0), equals(60000.0));

      // Test avec 100 clients (50% de l'objectif)
      expect(service.calculateSalary(100), equals(70000.0)); // 60000 + (100/200 * 20000)

      // Test avec 200 clients (objectif atteint)
      expect(service.calculateSalary(200), equals(80000.0)); // 60000 + 20000

      // Test avec plus de 200 clients (plafonné)
      expect(service.calculateSalary(300), equals(80000.0)); // Plafonné à 80000
    });

    test('calculateSalary should handle edge cases', () {
      // Test avec des valeurs négatives (ne devrait pas arriver mais on teste)
      expect(service.calculateSalary(-10), equals(60000.0));

      // Test avec exactement l'objectif
      expect(service.calculateSalary(200), equals(80000.0));

      // Test avec 1 client
      expect(service.calculateSalary(1), equals(60100.0)); // 60000 + (1/200 * 20000)
    });

    test('salary calculation should match the original function logic', () {
      // Fonction originale modifiée avec les nouveaux paramètres
      double calculerSalaireOriginal(int clientsValides) {
        const salaireFixe = 60000.0;
        const bonusMax = 20000.0;
        const objectifClients = 200;

        final clients = clientsValides.clamp(0, objectifClients);
        final bonus = (clients / objectifClients) * bonusMax;

        return salaireFixe + bonus;
      }

      // Test plusieurs valeurs pour s'assurer que notre service correspond
      for (int clients in [0, 50, 100, 150, 200, 250]) {
        expect(
          service.calculateSalary(clients),
          equals(calculerSalaireOriginal(clients)),
          reason: 'Failed for $clients clients',
        );
      }
    });
  });

  group('Salary Calculation Examples', () {
    test('should demonstrate salary calculation examples', () {
      final service = EmployeePerformanceService.instance;

      print('\n=== EXEMPLES DE CALCUL DE SALAIRE ===');
      print('Salaire fixe: 60 000 FCFA');
      print('Objectif: 200 clients avec commande ≥ 3 000 FCFA');
      print('Bonus maximum: 20 000 FCFA');
      print('Salaire maximum: 80 000 FCFA\n');

      final examples = [0, 25, 50, 100, 150, 200, 250];
      
      for (final clients in examples) {
        final salary = service.calculateSalary(clients);
        final percentage = (clients / 200 * 100).clamp(0, 100);
        final bonus = salary - 60000;
        
        print('$clients clients valides:');
        print('  - Progression: ${percentage.toStringAsFixed(1)}%');
        print('  - Bonus: ${bonus.toStringAsFixed(0)} FCFA');
        print('  - Salaire total: ${salary.toStringAsFixed(0)} FCFA\n');
      }
    });
  });
}
