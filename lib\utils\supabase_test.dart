import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/supabase_config.dart';

/// Utilitaire pour tester la configuration Supabase
class SupabaseTest {
  /// Teste la connexion Supabase avec la configuration actuelle
  static Future<Map<String, dynamic>> testConnection() async {
    final result = <String, dynamic>{
      'success': false,
      'mode': SupabaseConfig.useLocalMode ? 'local' : 'production',
      'url': SupabaseConfig.useLocalMode ? SupabaseConfig.localUrl : SupabaseConfig.url,
      'timestamp': DateTime.now().toIso8601String(),
      'errors': <String>[],
      'warnings': <String>[],
      'info': <String>[],
    };

    try {
      // Test de l'URL et de la clé
      final url = SupabaseConfig.useLocalMode ? SupabaseConfig.localUrl : SupabaseConfig.url;
      final anonKey = SupabaseConfig.useLocalMode ? SupabaseConfig.localAnonKey : SupabaseConfig.anonKey;
      
      result['info'].add('Mode: ${result['mode']}');
      result['info'].add('URL: $url');
      
      // Vérification de la configuration
      if (url == 'https://your-project-ref.supabase.co') {
        result['warnings'].add('URL de production non configurée (utilise un placeholder)');
      }
      
      if (anonKey == 'your-anon-key-here') {
        result['warnings'].add('Clé anonyme de production non configurée (utilise un placeholder)');
      }
      
      // Test d'initialisation
      await Supabase.initialize(
        url: url,
        anonKey: anonKey,
      );
      
      final client = Supabase.instance.client;
      result['info'].add('Client Supabase initialisé avec succès');
      
      // Test de connexion basique
      try {
        final response = await client
            .from('invoices')
            .select('count')
            .limit(1);
        
        result['info'].add('Test de requête réussi');
        result['success'] = true;
        
      } catch (e) {
        if (e.toString().contains('relation "invoices" does not exist')) {
          result['warnings'].add('Table "invoices" n\'existe pas encore');
          result['info'].add('Connexion établie mais tables non créées');
          result['success'] = true; // Connexion OK même si tables manquantes
        } else {
          result['errors'].add('Erreur de requête: $e');
        }
      }
      
    } catch (e) {
      result['errors'].add('Erreur d\'initialisation: $e');
      
      // Analyse des erreurs courantes
      final errorStr = e.toString().toLowerCase();
      
      if (errorStr.contains('failed host lookup') || errorStr.contains('socketexception')) {
        result['errors'].add('Problème de réseau: Impossible de résoudre l\'URL');
        if (SupabaseConfig.useLocalMode) {
          result['info'].add('Suggestion: Démarrez Supabase localement avec "supabase start"');
        } else {
          result['info'].add('Suggestion: Vérifiez votre URL Supabase et votre connexion internet');
        }
      }
      
      if (errorStr.contains('invalid api key')) {
        result['errors'].add('Clé API invalide');
        result['info'].add('Suggestion: Vérifiez votre clé anonyme Supabase');
      }
    }
    
    return result;
  }
  
  /// Affiche le résultat du test de manière formatée
  static void printTestResult(Map<String, dynamic> result) {
    print('\n=== TEST SUPABASE ===');
    print('Mode: ${result['mode']}');
    print('URL: ${result['url']}');
    print('Succès: ${result['success']}');
    print('Timestamp: ${result['timestamp']}');
    
    if (result['info'].isNotEmpty) {
      print('\n📋 Informations:');
      for (String info in result['info']) {
        print('  ℹ️  $info');
      }
    }
    
    if (result['warnings'].isNotEmpty) {
      print('\n⚠️  Avertissements:');
      for (String warning in result['warnings']) {
        print('  ⚠️  $warning');
      }
    }
    
    if (result['errors'].isNotEmpty) {
      print('\n❌ Erreurs:');
      for (String error in result['errors']) {
        print('  ❌ $error');
      }
    }
    
    print('\n===================\n');
  }
}