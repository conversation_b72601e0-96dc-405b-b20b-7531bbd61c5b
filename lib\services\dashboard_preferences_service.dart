import 'package:shared_preferences/shared_preferences.dart';

/// Types de cartes disponibles dans le tableau de bord
enum DashboardCardType {
  monthlyObjective,
  yearlyObjective,
  quickActions,
  performanceObjectives,
  challenges,
  invoiceStats,
  clientStats,
  productStats,
  recentInvoices,
  topClients,
  monthlyRevenue,
  communesStats,
}

/// Service pour gérer les préférences du tableau de bord
class DashboardPreferencesService {
  static final DashboardPreferencesService _instance =
      DashboardPreferencesService._internal();
  factory DashboardPreferencesService() => _instance;
  DashboardPreferencesService._internal();

  static DashboardPreferencesService get instance => _instance;

  static const String _cardOrderKey = 'dashboard_card_order';
  static const String _isReorderModeKey = 'dashboard_reorder_mode';

  /// Ordre par défaut des cartes
  static const List<DashboardCardType> _defaultOrder = [
    DashboardCardType.monthlyObjective,
    DashboardCardType.yearlyObjective,
    DashboardCardType.quickActions,
    DashboardCardType.performanceObjectives,
    DashboardCardType.challenges,
    DashboardCardType.invoiceStats,
    DashboardCardType.clientStats,
    DashboardCardType.productStats,
    DashboardCardType.recentInvoices,
    DashboardCardType.topClients,
    DashboardCardType.monthlyRevenue,
    DashboardCardType.communesStats,
  ];

  /// Obtenir l'ordre actuel des cartes
  Future<List<DashboardCardType>> getCardOrder() async {
    final prefs = await SharedPreferences.getInstance();
    final orderJson = prefs.getStringList(_cardOrderKey);

    if (orderJson == null) {
      return List.from(_defaultOrder);
    }

    try {
      final order =
          orderJson
              .map(
                (typeString) => DashboardCardType.values.firstWhere(
                  (type) => type.toString() == typeString,
                ),
              )
              .toList();

      // Vérifier que tous les types sont présents
      final missingTypes =
          _defaultOrder.where((type) => !order.contains(type)).toList();

      // Ajouter les types manquants à la fin
      order.addAll(missingTypes);

      return order;
    } catch (e) {
      // En cas d'erreur, retourner l'ordre par défaut
      return List.from(_defaultOrder);
    }
  }

  /// Sauvegarder l'ordre des cartes
  Future<void> saveCardOrder(List<DashboardCardType> order) async {
    final prefs = await SharedPreferences.getInstance();
    final orderJson = order.map((type) => type.toString()).toList();
    await prefs.setStringList(_cardOrderKey, orderJson);
  }

  /// Obtenir le mode de réorganisation
  Future<bool> getReorderMode() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isReorderModeKey) ?? false;
  }

  /// Activer/désactiver le mode de réorganisation
  Future<void> setReorderMode(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isReorderModeKey, enabled);
  }

  /// Réinitialiser l'ordre par défaut
  Future<void> resetToDefaultOrder() async {
    await saveCardOrder(_defaultOrder);
  }

  /// Déplacer une carte d'une position à une autre
  Future<void> moveCard(
    List<DashboardCardType> currentOrder,
    int oldIndex,
    int newIndex,
  ) async {
    final newOrder = List<DashboardCardType>.from(currentOrder);

    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    final item = newOrder.removeAt(oldIndex);
    newOrder.insert(newIndex, item);

    await saveCardOrder(newOrder);
  }

  /// Obtenir les informations d'affichage pour chaque type de carte
  DashboardCardInfo getCardInfo(DashboardCardType type) {
    switch (type) {
      case DashboardCardType.monthlyObjective:
        return const DashboardCardInfo(
          title: 'OBJECTIF MENSUEL',
          icon: 'trending_up',
          description: 'Progression du mois en cours',
        );
      case DashboardCardType.yearlyObjective:
        return const DashboardCardInfo(
          title: 'OBJECTIF ANNUEL',
          icon: 'date_range',
          description: 'Cumul des CA de l\'année',
        );
      case DashboardCardType.quickActions:
        return const DashboardCardInfo(
          title: 'ACTIONS RAPIDES',
          icon: 'flash_on',
          description: 'Accès rapide aux fonctions principales',
        );
      case DashboardCardType.performanceObjectives:
        return const DashboardCardInfo(
          title: 'PERFORMANCE & OBJECTIFS',
          icon: 'emoji_events',
          description: 'Suivi des performances et gamification',
        );
      case DashboardCardType.challenges:
        return const DashboardCardInfo(
          title: 'DÉFIS TEMPORAIRES',
          icon: 'flag',
          description: 'Défis et récompenses spéciales',
        );
      case DashboardCardType.invoiceStats:
        return const DashboardCardInfo(
          title: 'STATISTIQUES FACTURES',
          icon: 'receipt_long',
          description: 'Analyse des factures et revenus',
        );
      case DashboardCardType.clientStats:
        return const DashboardCardInfo(
          title: 'STATISTIQUES CLIENTS',
          icon: 'people',
          description: 'Analyse de la clientèle',
        );
      case DashboardCardType.productStats:
        return const DashboardCardInfo(
          title: 'STATISTIQUES PRODUITS',
          icon: 'inventory',
          description: 'Performance des produits',
        );
      case DashboardCardType.recentInvoices:
        return const DashboardCardInfo(
          title: 'FACTURES RÉCENTES',
          icon: 'history',
          description: 'Dernières factures créées',
        );
      case DashboardCardType.topClients:
        return const DashboardCardInfo(
          title: 'MEILLEURS CLIENTS',
          icon: 'star',
          description: 'Clients les plus actifs',
        );
      case DashboardCardType.monthlyRevenue:
        return const DashboardCardInfo(
          title: 'REVENUS MENSUELS',
          icon: 'trending_up',
          description: 'Évolution du chiffre d\'affaires',
        );
      case DashboardCardType.communesStats:
        return const DashboardCardInfo(
          title: 'COMMUNES SOLLICITÉES',
          icon: 'location_on',
          description: 'Répartition géographique',
        );
    }
  }
}

/// Informations d'affichage pour une carte
class DashboardCardInfo {
  final String title;
  final String icon;
  final String description;

  const DashboardCardInfo({
    required this.title,
    required this.icon,
    required this.description,
  });
}
