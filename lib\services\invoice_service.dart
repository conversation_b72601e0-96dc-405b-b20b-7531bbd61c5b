import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/invoice.dart';
import 'gamification_service.dart';
import 'firebase_service.dart';

class InvoiceService {
  static const String _invoicesKey = 'hcp_invoices';
  final Uuid _uuid = const Uuid();

  // Sauvegarder toutes les factures
  static Future<void> saveInvoices(List<Invoice> invoices) async {
    // Cette méthode n'est plus nécessaire avec le service hybride
    // Les factures sont sauvegardées individuellement
  }

  // Charger toutes les factures
  static Future<List<Invoice>> loadInvoices() async {
    try {
      // D'abord essayer de charger depuis le service hybride
      final hybridInvoices = await FirebaseService.instance.getAllInvoices();

      // Si le service hybride retourne des données, les utiliser
      if (hybridInvoices.isNotEmpty) {
        return hybridInvoices;
      }

      // Sinon, charger depuis SharedPreferences (données locales existantes)
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_invoicesKey);

      if (jsonString != null) {
        final jsonList = jsonDecode(jsonString) as List;
        final localInvoices =
            jsonList.map((json) => Invoice.fromJson(json)).toList();

        // Migrer les données locales vers Supabase en arrière-plan
        _migrateLocalInvoicesToSupabase(localInvoices);

        return localInvoices;
      }

      return [];
    } catch (e) {
      // En cas d'erreur, essayer de charger depuis SharedPreferences
      try {
        final prefs = await SharedPreferences.getInstance();
        final jsonString = prefs.getString(_invoicesKey);

        if (jsonString != null) {
          final jsonList = jsonDecode(jsonString) as List;
          return jsonList.map((json) => Invoice.fromJson(json)).toList();
        }
      } catch (localError) {
        // Ignorer les erreurs locales
      }

      return [];
    }
  }

  // Migrer les factures locales vers Supabase en arrière-plan
  static void _migrateLocalInvoicesToSupabase(List<Invoice> invoices) {
    Future.microtask(() async {
      try {
        for (final invoice in invoices) {
          await FirebaseService.instance.addInvoice(invoice);
        }
        // Optionnel : supprimer les données locales après migration réussie
        // final prefs = await SharedPreferences.getInstance();
        // await prefs.remove(_invoicesKey);
      } catch (e) {
        // Ignorer les erreurs de migration
      }
    });
  }

  // Ajouter une nouvelle facture
  Future<Invoice> addInvoice(Invoice invoice) async {
    final newInvoice = invoice.copyWith(id: _uuid.v4());
    await FirebaseService.instance.addInvoice(newInvoice);
    return newInvoice;
  }

  // Ajouter une nouvelle facture avec gamification
  Future<Map<String, dynamic>> addInvoiceWithGamification(
    Invoice invoice,
  ) async {
    final newInvoice = await addInvoice(invoice);

    // Déclencher la gamification seulement pour les factures payées
    GamificationReward? reward;
    if (newInvoice.status == InvoiceStatus.payee) {
      reward = await GamificationService.instance.processNewInvoice(newInvoice);
    }

    return {'invoice': newInvoice, 'reward': reward};
  }

  // Mettre à jour une facture existante
  Future<void> updateInvoice(Invoice updatedInvoice) async {
    final invoices = await InvoiceService.loadInvoices();
    final index = invoices.indexWhere(
      (invoice) => invoice.id == updatedInvoice.id,
    );

    if (index != -1) {
      invoices[index] = updatedInvoice;
      await InvoiceService.saveInvoices(invoices);
    }
  }

  // Mettre à jour une facture avec gamification
  Future<GamificationReward?> updateInvoiceWithGamification(
    Invoice originalInvoice,
    Invoice updatedInvoice,
  ) async {
    await updateInvoice(updatedInvoice);

    // Déclencher la gamification si le statut passe à "payée"
    if (originalInvoice.status != InvoiceStatus.payee &&
        updatedInvoice.status == InvoiceStatus.payee) {
      return await GamificationService.instance.processNewInvoice(
        updatedInvoice,
      );
    }

    return null;
  }

  // Supprimer une facture
  Future<void> deleteInvoice(String invoiceId) async {
    final invoices = await InvoiceService.loadInvoices();
    invoices.removeWhere((invoice) => invoice.id == invoiceId);
    await InvoiceService.saveInvoices(invoices);
  }

  // Obtenir une facture par ID
  Future<Invoice?> getInvoiceById(String id) async {
    final invoices = await InvoiceService.loadInvoices();
    try {
      return invoices.firstWhere((invoice) => invoice.id == id);
    } catch (e) {
      return null;
    }
  }

  // Filtrer les factures par statut
  Future<List<Invoice>> getInvoicesByStatus(InvoiceStatus status) async {
    final invoices = await InvoiceService.loadInvoices();
    return invoices.where((invoice) => invoice.status == status).toList();
  }

  // Rechercher des factures par nom de client
  Future<List<Invoice>> searchInvoicesByClientName(String query) async {
    final invoices = await InvoiceService.loadInvoices();
    final lowerQuery = query.toLowerCase();
    return invoices
        .where(
          (invoice) => invoice.clientName.toLowerCase().contains(lowerQuery),
        )
        .toList();
  }

  // Obtenir les statistiques des factures
  Future<Map<String, dynamic>> getInvoiceStats() async {
    final invoices = await InvoiceService.loadInvoices();

    double totalRevenue = 0;
    int paidCount = 0;
    int pendingCount = 0;
    int cancelledCount = 0;

    // Pour les communes les plus sollicitées
    Map<String, int> deliveryLocationCounts = {};
    final DateTime now = DateTime.now();
    final DateTime firstDayOfMonth = DateTime(now.year, now.month, 1);

    for (final invoice in invoices) {
      // Compter les livraisons par commune pour le mois en cours
      if (invoice.createdAt.isAfter(firstDayOfMonth)) {
        deliveryLocationCounts[invoice.deliveryLocation] =
            (deliveryLocationCounts[invoice.deliveryLocation] ?? 0) + 1;
      }

      switch (invoice.status) {
        case InvoiceStatus.payee:
          // CA = prix des articles - remises (sans frais de livraison)
          double subtotal = InvoiceService().calculateSubtotal(invoice.items);
          totalRevenue += subtotal;
          paidCount++;
          break;
        case InvoiceStatus.enAttente:
          pendingCount++;
          break;
        case InvoiceStatus.annulee:
          cancelledCount++;
          break;
      }
    }

    // Trier les communes par nombre de livraisons (décroissant)
    var sortedLocations =
        deliveryLocationCounts.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));

    // Prendre les 4 premières communes
    var topLocations = sortedLocations.take(4).toList();

    return {
      'totalInvoices': invoices.length,
      'totalRevenue': totalRevenue,
      'paidCount': paidCount,
      'pendingCount': pendingCount,
      'cancelledCount': cancelledCount,
      'topDeliveryLocations':
          topLocations
              .map((e) => {'location': e.key, 'count': e.value})
              .toList(),
    };
  }

  // Générer un ID unique pour les articles
  String generateItemId() {
    return _uuid.v4();
  }

  // Calculer le sous-total des articles
  double calculateSubtotal(List<InvoiceItem> items) {
    return items.fold(0, (sum, item) => sum + item.total);
  }

  // Calculer le total final
  double calculateTotal({
    required double subtotal,
    required double deliveryPrice,
    required double discountAmount, // Ajout du paramètre de remise
    double tipAmount = 0.0, // Ajout du paramètre de pourboire
    required double advance,
  }) {
    return subtotal + deliveryPrice + tipAmount - discountAmount - advance;
  }

  // Obtenir les statistiques de performance des employés pour le mois en cours
  Future<Map<String, dynamic>> getEmployeePerformanceStats() async {
    final invoices = await InvoiceService.loadInvoices();
    final DateTime now = DateTime.now();
    final DateTime startOfMonth = DateTime(now.year, now.month, 1);
    final DateTime endOfMonth = DateTime(
      now.year,
      now.month + 1,
      0,
      23,
      59,
      59,
    );

    // Filtrer les factures payées du mois en cours
    final monthlyPaidInvoices =
        invoices
            .where(
              (invoice) =>
                  invoice.status == InvoiceStatus.payee &&
                  invoice.createdAt.isAfter(startOfMonth) &&
                  invoice.createdAt.isBefore(endOfMonth),
            )
            .toList();

    // Grouper par client et calculer le total par client
    Map<String, double> clientTotals = {};
    for (final invoice in monthlyPaidInvoices) {
      final clientKey = '${invoice.clientName}_${invoice.clientNumber}';
      final subtotal = calculateSubtotal(invoice.items);
      clientTotals[clientKey] = (clientTotals[clientKey] ?? 0) + subtotal;
    }

    // Compter les clients valides (commande ≥ 3000 FCFA)
    const double minimumOrderAmount = 3000.0;
    final validClients =
        clientTotals.values
            .where((total) => total >= minimumOrderAmount)
            .length;

    // Calculer le salaire selon les nouveaux paramètres
    const double fixedSalary = 60000.0;
    const double maxBonus = 20000.0;
    const int clientObjective = 200;

    final double bonusPercentage = (validClients / clientObjective).clamp(
      0.0,
      1.0,
    );
    final double bonus = bonusPercentage * maxBonus;
    final double totalSalary = fixedSalary + bonus;

    // Calculer le CA total du mois
    final double monthlyRevenue = monthlyPaidInvoices.fold(
      0.0,
      (sum, invoice) => sum + calculateSubtotal(invoice.items),
    );

    return {
      'validClients': validClients,
      'clientObjective': clientObjective,
      'fixedSalary': fixedSalary,
      'bonus': bonus,
      'maxBonus': maxBonus,
      'totalSalary': totalSalary,
      'bonusPercentage': bonusPercentage * 100,
      'monthlyRevenue': monthlyRevenue,
      'totalInvoices': monthlyPaidInvoices.length,
      'averageOrderValue':
          monthlyPaidInvoices.isNotEmpty
              ? monthlyRevenue / monthlyPaidInvoices.length
              : 0.0,
      'objectiveProgress': (validClients / clientObjective * 100).clamp(
        0.0,
        100.0,
      ),
    };
  }
}
