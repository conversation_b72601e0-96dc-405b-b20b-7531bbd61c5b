import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service pour gérer les versions de l'application
class VersionService {
  static final VersionService _instance = VersionService._internal();
  factory VersionService() => _instance;
  VersionService._internal();

  static VersionService get instance => _instance;

  static const String _lastVersionKey = 'last_app_version';
  static const String _installDateKey = 'app_install_date';
  static const String _updateDateKey = 'app_update_date';

  PackageInfo? _packageInfo;

  /// Initialiser le service de version
  Future<void> initialize() async {
    _packageInfo = await PackageInfo.fromPlatform();
    await _checkForUpdate();
  }

  /// Obtenir les informations de version
  String get version => _packageInfo?.version ?? '2.0.1';
  String get buildNumber => _packageInfo?.buildNumber ?? '3';
  String get appName => _packageInfo?.appName ?? 'General HCP CRM';
  String get packageName => _packageInfo?.packageName ?? 'general_hcp_crm';

  /// Version complète (version + build)
  String get fullVersion => '$version+$buildNumber';

  /// Vérifier s'il y a eu une mise à jour
  Future<bool> _checkForUpdate() async {
    final prefs = await SharedPreferences.getInstance();
    final lastVersion = prefs.getString(_lastVersionKey);
    final currentVersion = fullVersion;

    if (lastVersion == null) {
      // Première installation
      await prefs.setString(_lastVersionKey, currentVersion);
      await prefs.setString(_installDateKey, DateTime.now().toIso8601String());
      return false;
    }

    if (lastVersion != currentVersion) {
      // Mise à jour détectée
      await prefs.setString(_lastVersionKey, currentVersion);
      await prefs.setString(_updateDateKey, DateTime.now().toIso8601String());
      await _clearCache();
      return true;
    }

    return false;
  }

  /// Nettoyer le cache après une mise à jour
  Future<void> _clearCache() async {
    final prefs = await SharedPreferences.getInstance();

    // Supprimer les clés de cache spécifiques
    final keysToRemove = [
      'dashboard_card_order',
      'cached_invoice_stats',
      'cached_inventory_stats',
      'cached_performance_data',
      'last_sync_timestamp',
    ];

    for (final key in keysToRemove) {
      await prefs.remove(key);
    }

    print('🧹 Cache nettoyé après mise à jour vers $fullVersion');
  }

  /// Obtenir la date d'installation
  Future<DateTime?> getInstallDate() async {
    final prefs = await SharedPreferences.getInstance();
    final dateString = prefs.getString(_installDateKey);
    return dateString != null ? DateTime.parse(dateString) : null;
  }

  /// Obtenir la date de dernière mise à jour
  Future<DateTime?> getLastUpdateDate() async {
    final prefs = await SharedPreferences.getInstance();
    final dateString = prefs.getString(_updateDateKey);
    return dateString != null ? DateTime.parse(dateString) : null;
  }

  /// Obtenir les informations de version pour l'affichage
  Future<Map<String, dynamic>> getVersionInfo() async {
    final installDate = await getInstallDate();
    final updateDate = await getLastUpdateDate();

    return {
      'version': version,
      'buildNumber': buildNumber,
      'fullVersion': fullVersion,
      'appName': appName,
      'packageName': packageName,
      'installDate': installDate?.toIso8601String(),
      'lastUpdateDate': updateDate?.toIso8601String(),
    };
  }

  /// Forcer le nettoyage du cache (pour debug)
  Future<void> forceClearCache() async {
    await _clearCache();
  }

  /// Obtenir le changelog pour cette version
  String getChangelog() {
    switch (version) {
      case '2.0.1':
        return '''
🎉 Version 2.0.1 - Système de versioning

✅ Nouvelles fonctionnalités :
• Système de versioning automatique complet
• Page d'informations de l'application avec version
• Scripts automatisés de mise à jour et build
• Gestion intelligente du cache avec nettoyage automatique

🔧 Améliorations :
• Suppression définitive du doublon d'objectif mensuel
• Nettoyage automatique du cache lors des mises à jour
• Interface d'informations de version dans le dashboard
• Documentation complète du système de versioning

📱 Interface :
• Nouveau bouton ℹ️ dans le dashboard pour les infos de version
• Page dédiée aux informations de l'application
• Affichage du changelog de la version actuelle
• Bouton de nettoyage manuel du cache

🛠️ Outils de développement :
• Script update_version.dart pour automatiser les mises à jour
• Script build_and_deploy.dart pour les builds optimisés
• Documentation VERSION_MANAGEMENT.md
• Génération automatique de changelog
        ''';
      case '2.0.0':
        return '''
🎉 Version 2.0.0 - Améliorations majeures

✅ Corrections apportées :
• Suppression du doublon d'objectif mensuel
• Amélioration de la validation du code d'authentification
• Messages d'erreur plus clairs pour l'authentification
• Optimisation des performances du dashboard
• Nettoyage automatique du cache lors des mises à jour

🔧 Améliorations techniques :
• Système de versioning automatique
• Gestion intelligente du cache
• Meilleure gestion des erreurs Firebase
• Interface utilisateur optimisée

📱 Interface :
• Dashboard plus épuré sans doublons
• Cartes réorganisées pour une meilleure expérience
• Animations améliorées
        ''';
      default:
        return 'Mise à jour de l\'application avec corrections et améliorations.';
    }
  }
}
