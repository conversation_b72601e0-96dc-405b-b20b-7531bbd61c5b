import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'pages/splash_screen.dart';
import 'services/notification_service.dart';
import 'services/sync_service.dart';
import 'services/platform_uniformity_service.dart';
import 'services/version_service.dart';
import 'services/offline_config_service.dart';
import 'services/data_migration_service.dart';
import 'scripts/init_admin.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await initializeDateFormatting('fr_FR', null);

  // Initialiser la configuration offline par défaut
  await OfflineConfigService.instance.initializeDefaultConfig();

  // Effectuer la migration des données si nécessaire
  await _performDataMigration();

  // Lancer l'application immédiatement
  runApp(const GeneralHCPCRMApp());

  // Initialiser les services en arrière-plan (seulement si pas en mode offline)
  _initializeServicesInBackground();
}

Future<void> _initializeServicesInBackground() async {
  try {
    // Vérifier si on peut utiliser les services réseau
    final canUseNetwork =
        await OfflineConfigService.instance.canPerformNetworkOperation();
    final canUseFirebase = await OfflineConfigService.instance.canUseFirebase();

    debugPrint('🔒 Mode offline: ${!canUseNetwork}');
    debugPrint('🔥 Firebase: ${canUseFirebase ? "activé" : "désactivé"}');

    // Initialiser l'administrateur par défaut (toujours nécessaire)
    await AdminInitializer.ensureAdminExists();
    debugPrint('Vérification de l\'administrateur terminée');

    // Initialiser Firebase seulement si autorisé
    if (canUseFirebase) {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      debugPrint('Firebase initialisé avec succès');
    } else {
      debugPrint('Firebase désactivé - mode offline');
    }

    // Initialiser le service de synchronisation seulement si autorisé
    final canAutoSync =
        await OfflineConfigService.instance.canPerformAutoSync();
    if (canAutoSync) {
      SyncService.instance.initializeInBackground();
      debugPrint('Service de synchronisation initialisé');
    } else {
      debugPrint('Synchronisation automatique désactivée - mode offline');
    }

    // Initialiser le service de notifications (local uniquement)
    await NotificationService().initialize();
    debugPrint('Service de notifications initialisé');

    // Démarrer les notifications périodiques seulement si autorisé
    final isOfflineMode =
        await OfflineConfigService.instance.isOfflineModeEnabled();
    if (!isOfflineMode) {
      await NotificationService().startPeriodicNotifications();
      debugPrint('Notifications périodiques démarrées');
    } else {
      debugPrint('Notifications périodiques désactivées - mode offline');
    }

    // Initialiser le service de version (local uniquement)
    await VersionService.instance.initialize();
    debugPrint('Service de version initialisé');
  } catch (e) {
    debugPrint('Erreur lors de l\'initialisation en arrière-plan: $e');
  }
}

/// Effectuer la migration des données
Future<void> _performDataMigration() async {
  try {
    debugPrint('🔄 Début de la migration des données...');

    // Obtenir la version actuelle
    await VersionService.instance.initialize();
    final currentVersion = VersionService.instance.version;

    // Effectuer la migration
    await DataMigrationService.instance.migrateToCurrentVersion(currentVersion);

    // Vérifier l'intégrité des données
    final isValid = await DataMigrationService.instance.verifyDataIntegrity();
    if (!isValid) {
      debugPrint('⚠️ Problème d\'intégrité des données détecté');
    }

    // Nettoyer les anciennes sauvegardes
    await DataMigrationService.instance.cleanupOldBackups();

    debugPrint('✅ Migration des données terminée');
  } catch (e) {
    debugPrint('❌ Erreur lors de la migration des données: $e');
    // Ne pas faire échouer l'application pour autant
  }
}

class GeneralHCPCRMApp extends StatelessWidget {
  const GeneralHCPCRMApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'General HCP CRM',
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [Locale('fr', 'FR'), Locale('en', 'US')],
      locale: const Locale('fr', 'FR'),
      theme: PlatformUniformityService.getUniformTheme(),
      home: const SplashScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
