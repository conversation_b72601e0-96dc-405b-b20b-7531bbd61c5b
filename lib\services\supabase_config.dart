/// Configuration Supabase pour HCP CRM
class SupabaseConfig {
  // URL et clé API Supabase - Configuration temporaire pour tests locaux
  // IMPORTANT: Remplacez ces valeurs par votre vraie configuration Supabase
  static const String url = 'https://your-project-ref.supabase.co';
  static const String anonKey = 'your-anon-key-here';
  
  // Configuration alternative pour développement local
  static const bool useLocalMode = true;
  static const String localUrl = 'http://localhost:54321';
  static const String localAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

  // Noms des tables dans Supabase
  static const String invoicesTable = 'invoices';
  static const String colisTable = 'colis';
  static const String productsTable = 'products';
  static const String tasksTable = 'tasks';
  static const String categoriesTable = 'categories';

  // Bucket pour les fichiers
  static const String storageBucket = 'hcp-crm-files';
}
