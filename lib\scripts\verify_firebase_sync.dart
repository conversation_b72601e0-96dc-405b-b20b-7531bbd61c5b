import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/firebase_service.dart';
import '../services/logging_service.dart';
import '../models/invoice.dart';
import '../models/colis.dart';
import '../models/task.dart';
import '../models/product.dart';

/// Script pour vérifier la synchronisation des données avec Firebase
class FirebaseSyncVerifier {
  static Future<void> verifySync() async {
    LoggingService.info('🔍 Début de la vérification de synchronisation Firebase', 'VERIFY');
    
    try {
      // Initialiser Firebase
      await FirebaseService.instance.initialize();
      
      if (!FirebaseService.instance.isOnline()) {
        LoggingService.error('❌ Firebase hors ligne - impossible de vérifier', 'VERIFY');
        return;
      }
      
      // Vérifier les factures
      await _verifyInvoices();
      
      // Vérifier les colis
      await _verifyColis();
      
      // Vérifier les tâches
      await _verifyTasks();
      
      // Vérifier les produits (note: non synchronisés)
      await _verifyProducts();
      
      LoggingService.success('✅ Vérification de synchronisation terminée', 'VERIFY');
      
    } catch (e) {
      LoggingService.error('❌ Erreur lors de la vérification', 'VERIFY', e);
    }
  }
  
  static Future<void> _verifyInvoices() async {
    LoggingService.info('📄 Vérification des factures...', 'VERIFY');
    
    try {
      // Données locales
      final prefs = await SharedPreferences.getInstance();
      final localInvoicesJson = prefs.getStringList('invoices') ?? [];
      
      LoggingService.info('📱 Factures locales trouvées: ${localInvoicesJson.length}', 'VERIFY');
      
      // Données Firebase
      final firebaseInvoices = await FirebaseService.instance.getAllInvoices();
      LoggingService.info('🔥 Factures Firebase trouvées: ${firebaseInvoices.length}', 'VERIFY');
      
      // Comparaison
      if (localInvoicesJson.length == firebaseInvoices.length) {
        LoggingService.success('✅ Nombre de factures identique', 'VERIFY');
      } else {
        LoggingService.warning(
          '⚠️ Différence détectée: ${localInvoicesJson.length} locales vs ${firebaseInvoices.length} Firebase',
          'VERIFY'
        );
      }
      
      // Afficher quelques exemples
      if (firebaseInvoices.isNotEmpty) {
        final firstInvoice = firebaseInvoices.first;
        LoggingService.info(
          '📋 Exemple facture Firebase: ID=${firstInvoice.id}, Total=${firstInvoice.total}, Status=${firstInvoice.status}',
          'VERIFY'
        );
      }
      
    } catch (e) {
      LoggingService.error('❌ Erreur vérification factures', 'VERIFY', e);
    }
  }
  
  static Future<void> _verifyColis() async {
    LoggingService.info('📮 Vérification des colis...', 'VERIFY');
    
    try {
      // Données locales
      final prefs = await SharedPreferences.getInstance();
      final localColisJson = prefs.getStringList('colis') ?? [];
      
      LoggingService.info('📱 Colis locaux trouvés: ${localColisJson.length}', 'VERIFY');
      
      // Données Firebase
      final firebaseColis = await FirebaseService.instance.getAllColis();
      LoggingService.info('🔥 Colis Firebase trouvés: ${firebaseColis.length}', 'VERIFY');
      
      // Comparaison
      if (localColisJson.length == firebaseColis.length) {
        LoggingService.success('✅ Nombre de colis identique', 'VERIFY');
      } else {
        LoggingService.warning(
          '⚠️ Différence détectée: ${localColisJson.length} locaux vs ${firebaseColis.length} Firebase',
          'VERIFY'
        );
      }
      
      // Afficher quelques exemples
      if (firebaseColis.isNotEmpty) {
        final firstColis = firebaseColis.first;
        LoggingService.info(
          '📦 Exemple colis Firebase: ID=${firstColis.id}, Statut=${firstColis.statut}, Reste=${firstColis.resteAPayer}',
          'VERIFY'
        );
      }
      
    } catch (e) {
      LoggingService.error('❌ Erreur vérification colis', 'VERIFY', e);
    }
  }
  
  static Future<void> _verifyTasks() async {
    LoggingService.info('✅ Vérification des tâches...', 'VERIFY');
    
    try {
      // Données locales
      final prefs = await SharedPreferences.getInstance();
      final localTasksJson = prefs.getStringList('tasks') ?? [];
      
      LoggingService.info('📱 Tâches locales trouvées: ${localTasksJson.length}', 'VERIFY');
      
      // Données Firebase
      final firebaseTasks = await FirebaseService.instance.getAllTasks();
      LoggingService.info('🔥 Tâches Firebase trouvées: ${firebaseTasks.length}', 'VERIFY');
      
      // Comparaison
      if (localTasksJson.length == firebaseTasks.length) {
        LoggingService.success('✅ Nombre de tâches identique', 'VERIFY');
      } else {
        LoggingService.warning(
          '⚠️ Différence détectée: ${localTasksJson.length} locales vs ${firebaseTasks.length} Firebase',
          'VERIFY'
        );
      }
      
      // Afficher quelques exemples
      if (firebaseTasks.isNotEmpty) {
        final firstTask = firebaseTasks.first;
        LoggingService.info(
          '📋 Exemple tâche Firebase: ID=${firstTask.id}, Titre=${firstTask.title}, Terminée=${firstTask.isCompleted}',
          'VERIFY'
        );
      }
      
    } catch (e) {
      LoggingService.error('❌ Erreur vérification tâches', 'VERIFY', e);
    }
  }
  
  static Future<void> _verifyProducts() async {
    LoggingService.info('📦 Vérification des produits...', 'VERIFY');
    
    try {
      // Données locales
      final prefs = await SharedPreferences.getInstance();
      final localProductsJson = prefs.getStringList('products') ?? [];
      
      LoggingService.info('📱 Produits locaux trouvés: ${localProductsJson.length}', 'VERIFY');
      
      // Note: Les produits ne sont PAS synchronisés avec Firebase dans ce projet
      LoggingService.info('ℹ️ Les produits restent locaux (non synchronisés avec Firebase)', 'VERIFY');
      
      // Vérifier quand même Firebase pour s'assurer qu'il n'y a pas de produits
      try {
        final firebaseProducts = await FirebaseService.instance.getAllProducts();
        LoggingService.info('🔥 Produits Firebase trouvés: ${firebaseProducts.length}', 'VERIFY');
        
        if (firebaseProducts.isNotEmpty) {
          LoggingService.warning(
            '⚠️ Attention: Des produits ont été trouvés dans Firebase alors qu\'ils ne devraient pas être synchronisés',
            'VERIFY'
          );
        }
      } catch (e) {
        LoggingService.info('ℹ️ Aucun produit dans Firebase (comportement attendu)', 'VERIFY');
      }
      
    } catch (e) {
      LoggingService.error('❌ Erreur vérification produits', 'VERIFY', e);
    }
  }
  
  /// Afficher un résumé détaillé des collections Firebase
  static Future<void> showFirebaseCollectionsSummary() async {
    LoggingService.info('📊 Résumé des collections Firebase', 'VERIFY');
    
    try {
      await FirebaseService.instance.initialize();
      
      if (!FirebaseService.instance.isOnline()) {
        LoggingService.error('❌ Firebase hors ligne', 'VERIFY');
        return;
      }
      
      // Collections synchronisées
      final invoices = await FirebaseService.instance.getAllInvoices();
      final colis = await FirebaseService.instance.getAllColis();
      final tasks = await FirebaseService.instance.getAllTasks();
      
      LoggingService.info('📊 RÉSUMÉ FIREBASE:', 'VERIFY');
      LoggingService.info('  📄 Factures: ${invoices.length}', 'VERIFY');
      LoggingService.info('  📮 Colis: ${colis.length}', 'VERIFY');
      LoggingService.info('  ✅ Tâches: ${tasks.length}', 'VERIFY');
      LoggingService.info('  📦 Produits: NON SYNCHRONISÉS (restent locaux)', 'VERIFY');
      LoggingService.info('  🏷️ Catégories: NON SYNCHRONISÉES (restent locales)', 'VERIFY');
      
    } catch (e) {
      LoggingService.error('❌ Erreur lors du résumé Firebase', 'VERIFY', e);
    }
  }
}

/// Point d'entrée pour exécuter la vérification
void main() async {
  await FirebaseSyncVerifier.verifySync();
  await FirebaseSyncVerifier.showFirebaseCollectionsSummary();
}