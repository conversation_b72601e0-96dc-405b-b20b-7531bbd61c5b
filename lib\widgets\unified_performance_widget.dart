import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../services/employee_performance_service.dart';
import '../services/gamification_service.dart' as gamification;
import '../pages/employee_performance_page.dart';

/// Widget unifié combinant performance employé et gamification
class UnifiedPerformanceWidget extends StatefulWidget {
  const UnifiedPerformanceWidget({super.key});

  @override
  State<UnifiedPerformanceWidget> createState() =>
      _UnifiedPerformanceWidgetState();
}

class _UnifiedPerformanceWidgetState extends State<UnifiedPerformanceWidget>
    with TickerProviderStateMixin {
  EmployeePerformanceStats? _performanceStats;
  gamification.GamificationStats? _gamificationStats;
  bool _isLoading = true;
  final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');
  final EmployeePerformanceService _performanceService =
      EmployeePerformanceService.instance;
  final gamification.GamificationService _gamificationService =
      gamification.GamificationService.instance;

  /// Formate une valeur monétaire de manière compacte
  String _formatCurrency(double value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(0)}K';
    } else {
      return value.toStringAsFixed(0);
    }
  }

  late AnimationController _pulseController;
  late AnimationController _progressController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadStats();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeOutCubic),
    );

    _pulseController.repeat(reverse: true);
  }

  Future<void> _loadStats() async {
    setState(() => _isLoading = true);
    try {
      final performanceStats =
          await _performanceService.getCurrentMonthPerformance();
      final gamificationStats =
          await _gamificationService.getGamificationStats();

      setState(() {
        _performanceStats = performanceStats;
        _gamificationStats = gamificationStats;
        _isLoading = false;
      });

      // Démarrer l'animation de progression
      _progressController.forward();
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors du chargement: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    if (_performanceStats == null || _gamificationStats == null) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(child: Text('Aucune donnée disponible')),
        ),
      );
    }

    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            colors: [
              Colors.indigo[400]!,
              Colors.purple[500]!,
              Colors.pink[400]!,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 20),
              _buildSalaryAndLevel(),
              const SizedBox(height: 20),
              _buildObjectiveProgress(),
              const SizedBox(height: 20),
              _buildStatsGrid(),
              const SizedBox(height: 16),
              _buildBadgesAndRewards(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _pulseAnimation.value,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.emoji_events,
                  color: Colors.white,
                  size: 28,
                ),
              ),
            );
          },
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Performance & Objectifs',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Text(
                _gamificationService.getRandomEncouragement(),
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ),
            ],
          ),
        ),
        Row(
          children: [
            IconButton(
              icon: const Icon(Icons.refresh, color: Colors.white),
              onPressed: _loadStats,
            ),
            IconButton(
              icon: const Icon(Icons.open_in_new, color: Colors.white),
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const EmployeePerformancePage(),
                  ),
                );
              },
              tooltip: 'Voir les détails',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSalaryAndLevel() {
    final performanceStats = _performanceStats!;
    final gamificationStats = _gamificationStats!;
    final pointsForNext = _gamificationService.getPointsForNextLevel(
      gamificationStats.level,
    );
    final progressToNext =
        gamificationStats.level < 10
            ? (gamificationStats.totalPoints % pointsForNext) / pointsForNext
            : 1.0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          // Salaire et Niveau côte à côte
          Row(
            children: [
              // Salaire
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.account_balance_wallet,
                          color: Colors.amber[300],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'Salaire Mensuel',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${_currencyFormat.format(performanceStats.totalSalary)} FCFA',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Bonus: ${_currencyFormat.format(performanceStats.bonus)} FCFA',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),

              // Séparateur vertical
              Container(
                height: 60,
                width: 1,
                color: Colors.white.withValues(alpha: 0.3),
                margin: const EdgeInsets.symmetric(horizontal: 16),
              ),

              // Niveau
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _getLevelIcon(gamificationStats.level),
                          color: Colors.amber[300],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'Niveau Gamification',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Niveau ${gamificationStats.level}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${gamificationStats.totalPoints} points',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Progression niveau
          if (gamificationStats.level < 10) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Progression niveau',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 12,
                  ),
                ),
                Text(
                  '${pointsForNext - (gamificationStats.totalPoints % pointsForNext)} points restants',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            AnimatedBuilder(
              animation: _progressAnimation,
              builder: (context, child) {
                return LinearProgressIndicator(
                  value: progressToNext * _progressAnimation.value,
                  backgroundColor: Colors.white.withValues(alpha: 0.3),
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.amber[300]!),
                  minHeight: 6,
                );
              },
            ),
          ] else
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.amber[300]!.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.emoji_events, color: Colors.amber[300]),
                  const SizedBox(width: 8),
                  Text(
                    '🏆 NIVEAU MAXIMUM ATTEINT !',
                    style: TextStyle(
                      color: Colors.amber[300],
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildObjectiveProgress() {
    final performanceStats = _performanceStats!;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(Icons.flag, color: Colors.orange[300], size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Objectif Mensuel',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Text(
                '${performanceStats.validClients}/${performanceStats.clientObjective}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.green[300],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return LinearProgressIndicator(
                value:
                    (performanceStats.objectiveProgress / 100) *
                    _progressAnimation.value,
                backgroundColor: Colors.white.withValues(alpha: 0.3),
                valueColor: AlwaysStoppedAnimation<Color>(
                  performanceStats.objectiveProgress >= 100
                      ? Colors.green[300]!
                      : Colors.orange[300]!,
                ),
                minHeight: 8,
              );
            },
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Clients avec commande ≥ 3 000 FCFA',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 12,
                ),
              ),
              Text(
                '${performanceStats.objectiveProgress.toStringAsFixed(1)}%',
                style: TextStyle(
                  color:
                      performanceStats.objectiveProgress >= 100
                          ? Colors.green[300]
                          : Colors.orange[300],
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatsGrid() {
    final performanceStats = _performanceStats!;
    final gamificationStats = _gamificationStats!;

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 4,
      childAspectRatio: 1.2,
      crossAxisSpacing: 8,
      mainAxisSpacing: 8,
      children: [
        _buildStatCard(
          'Série',
          '${gamificationStats.currentStreak}j',
          Icons.local_fire_department,
          Colors.orange[300]!,
        ),
        _buildStatCard(
          'Factures',
          '${performanceStats.totalInvoices}',
          Icons.receipt,
          Colors.green[300]!,
        ),
        _buildStatCard(
          'CA',
          _formatCurrency(performanceStats.totalRevenue),
          Icons.attach_money,
          Colors.blue[300]!,
        ),
        _buildStatCard(
          'Clients',
          '${performanceStats.totalClients}',
          Icons.people,
          Colors.pink[300]!,
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 4),
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 8,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildBadgesAndRewards() {
    final gamificationStats = _gamificationStats!;
    return Row(
      children: [
        // Badges
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Badges (${gamificationStats.badges.length})',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              if (gamificationStats.badges.isEmpty)
                Text(
                  'Aucun badge débloqué',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 11,
                    fontStyle: FontStyle.italic,
                  ),
                )
              else
                Wrap(
                  spacing: 4,
                  runSpacing: 4,
                  children:
                      gamificationStats.badges
                          .take(3)
                          .map((badge) => _buildBadgeChip(badge))
                          .toList(),
                ),
            ],
          ),
        ),

        // Récompense prochaine
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.amber[300]!.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.amber[300]!.withValues(alpha: 0.5),
            ),
          ),
          child: Column(
            children: [
              Icon(Icons.card_giftcard, color: Colors.amber[300], size: 16),
              const SizedBox(height: 4),
              Text(
                'Prochaine\nRécompense',
                style: TextStyle(
                  color: Colors.amber[300],
                  fontSize: 9,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBadgeChip(gamification.Badge badge) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: badge.color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: badge.color.withValues(alpha: 0.5)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(badge.icon, color: badge.color, size: 10),
          const SizedBox(width: 3),
          Text(
            badge.name,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 8,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getLevelIcon(int level) {
    if (level >= 10) return Icons.emoji_events;
    if (level >= 8) return Icons.diamond;
    if (level >= 6) return Icons.star;
    if (level >= 4) return Icons.trending_up;
    if (level >= 2) return Icons.thumb_up;
    return Icons.play_arrow;
  }
}
