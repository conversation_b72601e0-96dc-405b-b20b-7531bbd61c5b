import 'package:shared_preferences/shared_preferences.dart';
import 'supabase_service.dart';
import 'firebase_service.dart';
import 'logging_service.dart';
import '../utils/supabase_test.dart';

/// Service pour vérifier que vos données mobiles sont bien synchronisées
class SyncVerificationService {
  static final SyncVerificationService _instance =
      SyncVerificationService._internal();
  factory SyncVerificationService() => _instance;
  SyncVerificationService._internal();

  /// Vérifier que toutes vos données mobiles sont bien synchronisées
  Future<SyncVerificationReport> verifyDataIntegrity() async {
    LoggingService.info(
      '🔍 Vérification de l\'intégrité des données...',
      'VERIFICATION',
    );

    final report = SyncVerificationReport();

    try {
      // 1. Vérifier les données locales (SharedPreferences)
      await _verifyLocalData(report);

      // 2. Vérifier les données Supabase
      await _verifySupabaseData(report);

      // 3. Vérifier les données Firebase (si disponible)
      await _verifyFirebaseData(report);

      // 4. Comparer et générer le rapport
      _generateComparisonReport(report);

      LoggingService.success('✅ Vérification terminée', 'VERIFICATION');
    } catch (e) {
      LoggingService.error(
        '❌ Erreur lors de la vérification',
        'VERIFICATION',
        e,
      );
      report.hasErrors = true;
      report.errors.add('Erreur générale: $e');
    }

    return report;
  }

  /// Vérifier les données locales (vos données mobiles existantes)
  Future<void> _verifyLocalData(SyncVerificationReport report) async {
    LoggingService.info(
      '📱 Vérification des données locales...',
      'VERIFICATION',
    );

    try {
      final prefs = await SharedPreferences.getInstance();

      // Compter les factures locales
      final invoicesJson = prefs.getStringList('invoices') ?? [];
      report.localInvoicesCount = invoicesJson.length;

      // Compter les produits locaux
      final productsJson = prefs.getStringList('products') ?? [];
      report.localProductsCount = productsJson.length;

      // Compter les colis locaux
      final colisJson = prefs.getStringList('colis') ?? [];
      report.localColisCount = colisJson.length;

      // Compter les tâches locales
      final tasksJson = prefs.getStringList('tasks') ?? [];
      report.localTasksCount = tasksJson.length;

      // Compter les catégories locales
      final categoriesJson = prefs.getStringList('categories') ?? [];
      report.localCategoriesCount = categoriesJson.length;

      LoggingService.info(
        '📊 Données locales: ${report.localInvoicesCount} factures, '
            '${report.localProductsCount} produits, ${report.localColisCount} colis, '
            '${report.localTasksCount} tâches, ${report.localCategoriesCount} catégories',
        'VERIFICATION',
      );
    } catch (e) {
      LoggingService.error('❌ Erreur vérification locale', 'VERIFICATION', e);
      report.errors.add('Erreur données locales: $e');
    }
  }

  /// Vérifier les données Supabase
  Future<void> _verifySupabaseData(SyncVerificationReport report) async {
    LoggingService.info(
      '☁️ Vérification des données Supabase...',
      'VERIFICATION',
    );

    try {
      // Test de connexion Supabase d'abord
      final connectionTest = await SupabaseTest.testConnection();
      
      if (!connectionTest['success']) {
        // Ajouter les erreurs de connexion au rapport
        for (String error in connectionTest['errors']) {
          report.errors.add('Supabase: $error');
        }
        for (String warning in connectionTest['warnings']) {
          report.warnings.add('Supabase: $warning');
        }
        
        LoggingService.error('❌ Connexion Supabase échouée', 'VERIFICATION', null);
        return;
      }
      
      LoggingService.info('✅ Connexion Supabase réussie', 'VERIFICATION');
      
      // Si la connexion fonctionne, compter les données
      try {
        // Compter les factures Supabase
        final supabaseInvoices = await SupabaseService.getInvoices();
        report.supabaseInvoicesCount = supabaseInvoices.length;

        // Compter les produits Supabase
        final supabaseProducts = await SupabaseService.getProducts();
        report.supabaseProductsCount = supabaseProducts.length;

        // Compter les colis Supabase
        final supabaseColis = await SupabaseService.getColis();
        report.supabaseColisCount = supabaseColis.length;

        // Compter les tâches Supabase
        final supabaseTasks = await SupabaseService.getTasks();
        report.supabaseTasksCount = supabaseTasks.length;

        // Compter les catégories Supabase
        final supabaseCategories = await SupabaseService.getCategories();
        report.supabaseCategoriesCount = supabaseCategories.length;

        LoggingService.info(
          '📊 Données Supabase: ${report.supabaseInvoicesCount} factures, '
              '${report.supabaseProductsCount} produits, ${report.supabaseColisCount} colis, '
              '${report.supabaseTasksCount} tâches, ${report.supabaseCategoriesCount} catégories',
          'VERIFICATION',
        );
      } catch (e) {
        // Erreur lors de la récupération des données (tables manquantes, etc.)
        LoggingService.warning('⚠️ Tables Supabase non configurées', 'VERIFICATION');
        report.warnings.add('Tables Supabase non configurées: $e');
      }
      
    } catch (e) {
      LoggingService.error('❌ Erreur vérification Supabase', 'VERIFICATION', e);
      report.errors.add('Erreur données Supabase: $e');
    }
  }

  /// Vérifier les données Firebase
  Future<void> _verifyFirebaseData(SyncVerificationReport report) async {
    LoggingService.info(
      '🔥 Vérification des données Firebase...',
      'VERIFICATION',
    );

    try {
      final firebaseService = FirebaseService.instance;
      await firebaseService.initialize();

      if (firebaseService.isOnline()) {
        // Compter les factures Firebase
        final firebaseInvoices = await firebaseService.getAllInvoices();
        report.firebaseInvoicesCount = firebaseInvoices.length;

        // Compter les tâches Firebase
        final firebaseTasks = await firebaseService.getAllTasks();
        report.firebaseTasksCount = firebaseTasks.length;

        // Compter les colis Firebase
        final firebaseColis = await firebaseService.getAllColis();
        report.firebaseColisCount = firebaseColis.length;

        LoggingService.info(
          '📊 Données Firebase: ${report.firebaseInvoicesCount} factures, '
              '${report.firebaseTasksCount} tâches, ${report.firebaseColisCount} colis',
          'VERIFICATION',
        );
      } else {
        LoggingService.warning('⚠️ Firebase hors ligne', 'VERIFICATION');
        report.warnings.add('Firebase non disponible pour vérification');
      }
    } catch (e) {
      LoggingService.warning(
        '⚠️ Erreur vérification Firebase (ignorée)',
        'VERIFICATION',
      );
      report.warnings.add('Firebase non accessible: $e');
    }
  }

  /// Générer le rapport de comparaison
  void _generateComparisonReport(SyncVerificationReport report) {
    LoggingService.info(
      '📋 Génération du rapport de comparaison...',
      'VERIFICATION',
    );

    // Vérifier si les données locales sont synchronisées avec Supabase
    if (report.localInvoicesCount > 0 && report.supabaseInvoicesCount == 0) {
      report.warnings.add('Factures locales non synchronisées avec Supabase');
    }

    if (report.localProductsCount > 0 && report.supabaseProductsCount == 0) {
      report.warnings.add('Produits locaux non synchronisés avec Supabase');
    }

    if (report.localColisCount > 0 && report.supabaseColisCount == 0) {
      report.warnings.add('Colis locaux non synchronisés avec Supabase');
    }

    if (report.localTasksCount > 0 && report.supabaseTasksCount == 0) {
      report.warnings.add('Tâches locales non synchronisées avec Supabase');
    }

    // Calculer le pourcentage de synchronisation
    final totalLocal =
        report.localInvoicesCount +
        report.localProductsCount +
        report.localColisCount +
        report.localTasksCount +
        report.localCategoriesCount;

    final totalSupabase =
        report.supabaseInvoicesCount +
        report.supabaseProductsCount +
        report.supabaseColisCount +
        report.supabaseTasksCount +
        report.supabaseCategoriesCount;

    if (totalLocal > 0) {
      report.syncPercentage = (totalSupabase / totalLocal * 100).clamp(0, 100);
    } else {
      report.syncPercentage = 100; // Pas de données locales = 100% synchronisé
    }

    // Déterminer le statut global
    if (report.errors.isNotEmpty) {
      report.status = SyncStatus.error;
    } else if (report.syncPercentage < 50) {
      report.status = SyncStatus.critical;
    } else if (report.syncPercentage < 90) {
      report.status = SyncStatus.warning;
    } else {
      report.status = SyncStatus.success;
    }

    LoggingService.info(
      '📊 Synchronisation: ${report.syncPercentage.toStringAsFixed(1)}% - Statut: ${report.status}',
      'VERIFICATION',
    );
  }

  /// Afficher un rapport détaillé dans la console
  void printDetailedReport(SyncVerificationReport report) {
    print('\n${'=' * 60}');
    print('📋 RAPPORT DE SYNCHRONISATION DES DONNÉES');
    print('=' * 60);
    print('📱 DONNÉES LOCALES (Mobile):');
    print('   • Factures: ${report.localInvoicesCount}');
    print('   • Produits: ${report.localProductsCount}');
    print('   • Colis: ${report.localColisCount}');
    print('   • Tâches: ${report.localTasksCount}');
    print('   • Catégories: ${report.localCategoriesCount}');
    print('');
    print('☁️ DONNÉES SUPABASE (Web):');
    print('   • Factures: ${report.supabaseInvoicesCount}');
    print('   • Produits: ${report.supabaseProductsCount}');
    print('   • Colis: ${report.supabaseColisCount}');
    print('   • Tâches: ${report.supabaseTasksCount}');
    print('   • Catégories: ${report.supabaseCategoriesCount}');
    print('');
    print('🔥 DONNÉES FIREBASE:');
    print('   • Factures: ${report.firebaseInvoicesCount}');
    print('   • Tâches: ${report.firebaseTasksCount}');
    print('   • Colis: ${report.firebaseColisCount}');
    print('');
    print('📊 RÉSUMÉ:');
    print('   • Synchronisation: ${report.syncPercentage.toStringAsFixed(1)}%');
    print('   • Statut: ${report.status}');
    print('');
    if (report.warnings.isNotEmpty) {
      print('⚠️ AVERTISSEMENTS:');
      for (final warning in report.warnings) {
        print('   • $warning');
      }
      print('');
    }
    if (report.errors.isNotEmpty) {
      print('❌ ERREURS:');
      for (final error in report.errors) {
        print('   • $error');
      }
      print('');
    }
    print('=' * 60);
  }
}

/// Rapport de vérification de synchronisation
class SyncVerificationReport {
  // Données locales
  int localInvoicesCount = 0;
  int localProductsCount = 0;
  int localColisCount = 0;
  int localTasksCount = 0;
  int localCategoriesCount = 0;

  // Données Supabase
  int supabaseInvoicesCount = 0;
  int supabaseProductsCount = 0;
  int supabaseColisCount = 0;
  int supabaseTasksCount = 0;
  int supabaseCategoriesCount = 0;

  // Données Firebase
  int firebaseInvoicesCount = 0;
  int firebaseTasksCount = 0;
  int firebaseColisCount = 0;

  // Statut
  SyncStatus status = SyncStatus.unknown;
  double syncPercentage = 0.0;
  bool hasErrors = false;

  // Messages
  List<String> warnings = [];
  List<String> errors = [];
}

/// Statut de synchronisation
enum SyncStatus { unknown, success, warning, critical, error }

extension SyncStatusExtension on SyncStatus {
  String get displayName {
    switch (this) {
      case SyncStatus.success:
        return '✅ Succès';
      case SyncStatus.warning:
        return '⚠️ Avertissement';
      case SyncStatus.critical:
        return '🚨 Critique';
      case SyncStatus.error:
        return '❌ Erreur';
      case SyncStatus.unknown:
        return '❓ Inconnu';
    }
  }
}
